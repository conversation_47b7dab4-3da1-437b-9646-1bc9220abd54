import { Suspense } from "react"
import { auth } from "@clerk/nextjs/server"
import { redirect } from "next/navigation"
import { getChannels } from "@/actions/chatActions"
import { fetchNotesTreeData } from "@/actions/treeActions"
import { getSavedDiagrams } from "@/app/actions/diagram"
import { getActivityData } from "@/app/actions/activity"
import { getNotes } from "@/actions/actions"
import WorkspaceContent, { QuickAccessCards } from "@/components/workspace/workspace-content"
import { NavbarWorkspace } from "@/components/workspace/navbarworkspace"
import { ActivityGraph } from "@/components/workspace/activity-graph"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Clock, FileText, MessageSquare, Plus, Search, User2 } from "lucide-react"
import Link from "next/link"
import { format, formatDistanceToNow } from "date-fns"
import Image from "next/image"
import { CreateWorkflowButton, OpenWorkflowButton } from "./workflow-actions"
import { ChatSection } from "@/components/workspace/chat-section"
import { NavbarSkeleton } from "@/components/workspace/skeletons/navbar-skeleton"

// Improved ActivitySkeleton component
const ActivitySkeleton = () => (
  <Card className="w-full animate-pulse">
    <CardHeader className="pb-2">
      <div className="w-48 h-6 bg-muted/40 rounded mb-2"></div>
      <div className="w-64 h-4 bg-muted/30 rounded"></div>
    </CardHeader>
    <CardContent className="h-[300px] py-6">
      <div className="h-full w-full flex flex-col">
        {/* Chart axes */}
        <div className="flex h-full">
          {/* Y-axis labels */}
          <div className="w-10 h-full flex flex-col justify-between pb-6">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="w-6 h-3 bg-muted/30 rounded"></div>
            ))}
          </div>
          
          {/* Chart area with bars */}
          <div className="flex-1 h-full flex items-end justify-between gap-1 px-2 pb-6">
            {Array.from({ length: 15 }).map((_, i) => (
              <div key={i} className="w-full flex flex-col items-center gap-1">
                {/* Stacked bars with different colors */}
                <div 
                  className="w-full bg-blue-200/40 rounded-t-sm"
                  style={{ height: `${Math.random() * 40 + 20}px` }}
                ></div>
                <div 
                  className="w-full bg-green-200/40 rounded-t-sm"
                  style={{ height: `${Math.random() * 30 + 15}px` }}
                ></div>
                <div 
                  className="w-full bg-red-200/40 rounded-t-sm"
                  style={{ height: `${Math.random() * 50 + 10}px` }}
                ></div>
              </div>
            ))}
          </div>
        </div>
        
        {/* X-axis labels */}
        <div className="h-6 flex justify-between px-12">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="w-12 h-3 bg-muted/30 rounded"></div>
          ))}
        </div>
        
        {/* Legend */}
        <div className="flex justify-center gap-4 mt-4">
          {['Notes', 'Workflows', 'Chats'].map((label, i) => (
            <div key={label} className="flex items-center gap-2">
              <div 
                className={`w-3 h-3 rounded-full ${
                  i === 0 ? 'bg-blue-200/60' : 
                  i === 1 ? 'bg-green-200/60' : 
                  'bg-red-200/60'
                }`}
              ></div>
              <div className="w-16 h-3 bg-muted/30 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    </CardContent>
  </Card>
)

const WorkflowSkeleton = () => (
  <div className="w-full rounded-lg bg-muted/20 animate-pulse">
    <div className="h-16 rounded-t-lg bg-muted/30 mb-4 flex items-center justify-between px-4">
      <div className="space-y-2">
        <div className="w-40 h-5 bg-muted/40 rounded"></div>
        <div className="w-60 h-4 bg-muted/30 rounded"></div>
      </div>
      <div className="w-32 h-9 bg-muted/40 rounded"></div>
    </div>
    <div className="px-4 pb-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="h-48 rounded-lg bg-muted/30 p-4">
            <div className="flex justify-between">
              <div className="w-32 h-5 bg-muted/40 rounded"></div>
              <div className="w-16 h-5 bg-muted/40 rounded"></div>
            </div>
            <div className="mt-4 space-y-2">
              <div className="w-full h-4 bg-muted/30 rounded"></div>
              <div className="w-2/3 h-4 bg-muted/30 rounded"></div>
            </div>
            <div className="mt-6 w-full h-8 bg-muted/40 rounded"></div>
          </div>
        ))}
      </div>
    </div>
  </div>
)

const NotesSkeleton = () => (
  <div className="w-full rounded-lg bg-muted/20 animate-pulse">
    <div className="h-16 rounded-t-lg bg-muted/30 mb-4 flex items-center justify-between px-4">
      <div className="space-y-2">
        <div className="w-40 h-5 bg-muted/40 rounded"></div>
        <div className="w-60 h-4 bg-muted/30 rounded"></div>
      </div>
      <div className="flex gap-4">
        <div className="w-64 h-9 bg-muted/30 rounded"></div>
        <div className="w-32 h-9 bg-muted/40 rounded"></div>
      </div>
    </div>
    <div className="px-4 pb-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="h-28 rounded-lg bg-muted/30 p-4">
            <div className="w-40 h-5 bg-muted/40 rounded mb-4"></div>
            <div className="flex justify-between mt-4">
              <div className="w-20 h-4 bg-muted/30 rounded"></div>
              <div className="w-12 h-4 bg-muted/30 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
)

// Server components for sections with data fetching
async function ActivitySection() {
  // Add artificial delay to ensure we see the skeleton (for development)
  // await new Promise(resolve => setTimeout(resolve, 2000));
  
  const activityData = await getActivityData();
  
  const formattedData = activityData.success && activityData.data 
    ? {
        notes: activityData.data.notes || [],
        workflows: activityData.data.workflows || [],
        chats: activityData.data.chats || []
      } 
    : { notes: [], workflows: [], chats: [] };
    
  return (
    <ActivityGraph
      data={formattedData}
    />
  );
}

async function WorkflowSection() {
  const workflowsResult = await getSavedDiagrams();
  
  const workflows = workflowsResult.success && workflowsResult.data 
    ? workflowsResult.data.map(workflow => {
        const content = workflow.content || {};
        const safeContent = {
          nodes: Array.isArray((content as any)?.nodes) ? (content as any).nodes : [],
          edges: Array.isArray((content as any)?.edges) ? (content as any).edges : []
        };
        
        return {
          id: workflow.id,
          title: workflow.title,
          content: safeContent,
          clerkId: workflow.clerkId,
          isPublic: workflow.isPublic,
          publicId: workflow.publicId,
          createdAt: workflow.createdAt,
          updatedAt: workflow.updatedAt
        };
      }) 
    : [];
  
  return (
    <Card className="relative border-border/50">
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500/20 via-primary/20 to-transparent" />
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle className="text-2xl">My Workflows</CardTitle>
          <CardDescription>Your saved workflow diagrams</CardDescription>
        </div>
        <CreateWorkflowButton />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {workflows.map((workflow) => (
            <div
              key={workflow.id}
              className="opacity-100 transition-all duration-300"
            >
              <Card className="group hover:shadow-lg transition-all duration-300 border-border/50">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg font-semibold line-clamp-1">
                        {workflow.title}
                      </CardTitle>
                      <CardDescription className="mt-1">
                        Created {formatDistanceToNow(new Date(workflow.createdAt))} ago
                      </CardDescription>
                    </div>
                    {workflow.isPublic && (
                      <Badge variant="outline">Public</Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="space-y-4">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-primary/60 mr-2" />
                        <span>{workflow.content.nodes.length} Nodes</span>
                      </div>
                      <div className="flex items-center ml-4">
                        <div className="w-2 h-2 rounded-full bg-primary/60 mr-2" />
                        <span>{workflow.content.edges.length} Connections</span>
                      </div>
                    </div>
                    <OpenWorkflowButton id={workflow.id} />
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

async function NotesSection() {
  const notesData = await getNotes();
  const treeData = await fetchNotesTreeData();
  
  const safeTreeData = treeData && 'data' in treeData && Array.isArray(treeData.data) 
    ? treeData.data 
    : [];
  
  const notes = notesData.success && notesData.notes ? notesData.notes.map(note => ({
    id: note.id,
    title: note.title,
    content: String(note.content),
    coverImage: note.coverImage,
    createdAt: note.createdAt,
    updatedAt: note.updatedAt
  })) : [];
  
  const filteredNotes = safeTreeData
    .filter(note => note.type === 'file')
    .slice(0, 6);
  
  return (
    <Card className="relative border-border/50">
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-emerald-500/20 via-primary/20 to-transparent" />
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl">Notes Evolution</CardTitle>
            <CardDescription>Track your notes progress</CardDescription>
          </div>
          <div className="flex items-center gap-4">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search notes..."
                className="pl-8 w-64"
                readOnly
              />
            </div>
            <Button asChild>
              <Link href="/hr/workspace/note">
                <Plus className="w-4 h-4 mr-2" />
                New Note
              </Link>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredNotes.map((note) => (
              <div
                key={note.id}
                className="opacity-100 transition-all duration-200"
              >
                <Link href={`/hr/workspace/note/${note.id}`}>
                  <Card
                    className="cursor-pointer hover:shadow-lg transition-all group border-border/50 hover:scale-[1.02]"
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base line-clamp-1">{note.name}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardFooter className="pt-2 text-xs text-muted-foreground">
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {new Date(note.createdAt || '').toLocaleDateString()}
                        </div>
                        <div className="flex items-center">
                          <User2 className="w-3 h-3 mr-1" />
                          You
                        </div>
                      </div>
                    </CardFooter>
                  </Card>
                </Link>
              </div>
            ))}
          </div>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 mt-6">
            {notes.slice(0, 12).map((note) => (
              <Link key={note.id} href={`/hr/workspace/note/${note.id}`}>
                <Card
                  className="hover:shadow-lg transition-shadow overflow-hidden cursor-pointer group"
                >
                  <div className="relative w-full h-24">
                    {note.coverImage ? (
                      <Image
                        src={note.coverImage}
                        alt={note.title}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 16vw"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                        <FileText className="w-6 h-6 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div className="p-2">
                    <h3 className="text-sm font-medium line-clamp-1 group-hover:text-blue-600">
                      {note.title}
                    </h3>
                    <p className="text-xs text-gray-500 mt-1">
                      {format(new Date(note.createdAt), 'MMM dd, yyyy')}
                    </p>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default async function WorkspacePage() {
  const { userId } = auth();

  if (!userId) {
    redirect('/sign-in');
  }

  // Fetch initial channel data immediately to get the channelId
  const channelsResult = await getChannels()
  
  const currentChannelId = channelsResult.success && channelsResult.channels.length > 0
    ? channelsResult.channels[0].id
    : ""

  return (
    <div className="min-h-screen flex flex-col">
      <Suspense fallback={<NavbarSkeleton />}>
        <NavbarWorkspace
          channelId={currentChannelId}
          notifications={[
            {
              id: "1",
              title: "New Channel Invitation",
              message: "You've been invited to join #general",
              time: "2 minutes ago"
            }
          ]}
          invitedBy={{
            name: "LoopFlowSpace",
          }}
        />
      </Suspense>

      <div className="flex-1 container mx-auto p-6 mt-8">
        {/* Quick Access Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <QuickAccessCards />
        </div>

        {/* Main Grid Layout */}
        <div className="grid grid-cols-12 gap-6 relative">
          {/* Activity Graph with Suspense */}
          <div className="col-span-12">
            <Suspense fallback={<ActivitySkeleton />}>
              <ActivitySection />
            </Suspense>
          </div>

          {/* Workflows Section with Suspense */}
          <div className="col-span-12 lg:col-span-8">
            <Suspense fallback={<WorkflowSkeleton />}>
              <WorkflowSection />
            </Suspense>
          </div>

          {/* Team Chat Card */}
          <div className="col-span-12 lg:col-span-4">
            <Suspense fallback={<div className="h-[500px] bg-muted/20 animate-pulse rounded-lg"></div>}>
              <ChatSection 
                initialChannels={channelsResult.success ? channelsResult.channels.map(channel => ({
                  id: channel.id,
                  name: channel.name,
                  description: channel.description || undefined,
                  role: channel.role,
                  isCreator: channel.isCreator
                })) : []}
              />
            </Suspense>
          </div>

          {/* Notes Section with Suspense */}
          <div className="col-span-12">
            <Suspense fallback={<NotesSkeleton />}>
              <NotesSection />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  )
}



