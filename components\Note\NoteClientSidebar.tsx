"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  ChevronRight,
  Folder,
  FileText,
  Plus,
  ChevronLeft,
  Search,
  Clock,
  Globe2,
  Users,
  MoreHorizontal,
  Save,
  Trash2
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { getNotes, createFolder, updateNoteParent, deleteNote } from "@/actions/actions";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
// Import headless-tree components
import {
  createOnDropHandler,
  dragAndDropFeature,
  hotkeysCoreFeature,
  keyboardDragAndDropFeature,
  selectionFeature,
  syncDataLoaderFeature,
} from "@headless-tree/core";
import { AssistiveTreeDescription, useTree } from "@headless-tree/react";
import { Tree, TreeItem, TreeItemLabel } from '@/components/ui/tree';

// Interface for note items compatible with headless-tree
interface NoteItem {
  id: string;
  title: string;
  isFolder: boolean;
  parentId?: string | null;
  children?: string[];
  createdAt: string;
  updatedAt: string;
  isPublished?: boolean;
  metadata?: any;
  content?: string;
}

export default function NoteClientSidebar() {
  const [notes, setNotes] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [folderName, setFolderName] = useState("");
  const [parentFolderId, setParentFolderId] = useState<string | undefined>(undefined);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<any | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    fetchNotes();
  }, []);

  // Transform notes into headless-tree compatible format
  const transformNotesToTreeItems = (notes: any[]): Record<string, NoteItem> => {
    const items: Record<string, NoteItem> = {};

    // Add root item
    items.root = {
      id: 'root',
      title: 'Workspace',
      isFolder: true,
      children: notes.filter(note => !note.parentId).map(note => note.id),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Add all notes
    notes.forEach(note => {
      const children = notes.filter(n => n.parentId === note.id).map(n => n.id);
      items[note.id] = {
        ...note,
        children: children.length > 0 ? children : undefined,
      };
    });

    return items;
  };

  const treeItems = transformNotesToTreeItems(notes);

  // Initialize headless-tree
  const tree = useTree<NoteItem>({
    initialState: {
      expandedItems: pathname.includes('/note/')
        ? (() => {
            const noteId = pathname.split('/').pop();
            const note = notes.find((n: any) => n.id === noteId);
            const expandedItems = ['root'];
            if (note && note.parentId) {
              expandedItems.push(note.parentId);
            }
            return expandedItems;
          })()
        : ['root'],
      selectedItems: pathname.includes('/note/')
        ? [pathname.split('/').pop() || '']
        : [],
    },
    indent: 20,
    rootItemId: "root",
    getItemName: (item) => item.getItemData()?.title ?? "Unknown",
    isItemFolder: (item) => item.getItemData()?.isFolder ?? false,
    canReorder: true,
    onDrop: createOnDropHandler(async (parentItem, newChildrenIds) => {
      const parentId = parentItem.getId() === 'root' ? null : parentItem.getId();

      // Find which item was moved by comparing with current structure
      const currentChildren = treeItems[parentItem.getId()]?.children || [];
      const movedItemId = newChildrenIds.find(id => !currentChildren.includes(id)) ||
                         currentChildren.find(id => !newChildrenIds.includes(id));

      if (movedItemId && movedItemId !== 'root') {
        try {
          const result = await updateNoteParent(movedItemId, parentId);
          if (result.success) {
            toast.success('Item moved successfully');
            fetchNotes(); // Refresh the notes
          } else {
            toast.error(result.error || 'Failed to move item');
          }
        } catch (error) {
          console.error('Error moving item:', error);
          toast.error('An error occurred while moving the item');
        }
      }
    }),
    dataLoader: {
      getItem: (itemId) => treeItems[itemId],
      getChildren: (itemId) => treeItems[itemId]?.children ?? [],
    },
    features: [
      syncDataLoaderFeature,
      selectionFeature,
      hotkeysCoreFeature,
      dragAndDropFeature,
      keyboardDragAndDropFeature,
    ],
  });

  const fetchNotes = async () => {
    setIsLoading(true);
    try {
      const response = await getNotes();
      // Check if the response was successful and contains notes
      if (response.success && response.notes) {
        setNotes(response.notes);
      } else {
        setNotes([]);
      }
    } catch (error) {
      console.error("Failed to fetch notes", error);
      setNotes([]);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const isNoteActive = (noteId: string) => pathname === `/hr/workspace/note/${noteId}`;

  // Format date helper
  const formatDate = (date: string | Date) => {
    if (!date) return "";
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, 'MMM d, yyyy');
  };

  // Helper function to get icon for notes
  const getNoteIcon = (note: NoteItem) => {
    if (note.isFolder) {
      return <Folder className="h-3.5 w-3.5 text-muted-foreground" />;
    }

    // Parse metadata for collaborative info
    let isCollaborative = false;
    try {
      if (note.metadata) {
        const metadata = typeof note.metadata === 'string'
          ? JSON.parse(note.metadata)
          : note.metadata;
        isCollaborative = !!metadata.collaborationId;
      }
    } catch (e) {
      console.error('Error parsing metadata:', e);
    }

    if (isCollaborative) {
      return <Users className="text-blue-400 h-3.5 w-3.5" />;
    } else if (note.isPublished) {
      return <Globe2 className="text-green-400 h-3.5 w-3.5" />;
    } else {
      return <FileText className="h-3.5 w-3.5 text-muted-foreground" />;
    }
  };

  // Open folder creation dialog
  const openFolderDialog = (parentId?: string) => {
    setParentFolderId(parentId);
    setFolderName("");
    setIsDialogOpen(true);
  };

  // Create new folder
  const handleCreateFolder = async () => {
    if (!folderName.trim()) {
      toast.error("Folder name cannot be empty");
      return;
    }

    try {
      const result = await createFolder(folderName, parentFolderId);
      if (result.success) {
        toast.success(`Folder "${folderName}" created`);
        fetchNotes(); // Refresh notes
        setIsDialogOpen(false);
      } else {
        toast.error(result.error || 'Failed to create folder');
      }
    } catch (error) {
      console.error('Error creating folder:', error);
      toast.error('An error occurred while creating folder');
    }
  };

  // Create new note inside a folder
  const handleCreateNote = (parentId?: string) => {
    router.push('/hr/workspace/note' + (parentId ? `?parentId=${parentId}` : ''));
  };

  // Handle note selection
  const handleNoteSelect = (noteId: string) => {
    if (noteId !== 'root') {
      router.push(`/hr/workspace/note/${noteId}`);
    }
  };

  // Add this function to handle delete
  const handleDeleteItem = async (item: any) => {
    setItemToDelete(item);
    setIsDeleteDialogOpen(true);
  };

  // Add this function to confirm deletion
  const confirmDelete = async () => {
    if (!itemToDelete) return;

    toast.promise(
      deleteNote(itemToDelete.id),
      {
        loading: `Deleting ${itemToDelete.isFolder ? 'folder' : 'note'}...`,
        success: (result) => {
          if (result.success) {
            fetchNotes(); // Refresh the notes list
            return `${itemToDelete.isFolder ? 'Folder' : 'Note'} deleted successfully`;
          } else {
            throw new Error(result.error);
          }
        },
        error: (err) => `Failed to delete: ${err.message || 'Unknown error'}`
      }
    );

    setIsDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  // Get published notes
  const publishedNotes = notes.filter(note => note.isPublished);

  // Get recent notes (last 7 days, non-folders)
  const recentNotes = notes
    .filter(note => !note.isFolder && new Date(note.updatedAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, 5);

  // Render the new tree using headless-tree
  const renderNewTree = () => {
    if (searchQuery.trim() !== "") {
      // For search, fall back to simple list view
      const filteredNotes = notes.filter(note =>
        note.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (typeof note.content === 'string' && note.content.toLowerCase().includes(searchQuery.toLowerCase()))
      );

      return (
        <div className="space-y-1">
          {filteredNotes.map(note => (
            <div
              key={note.id}
              onClick={() => !note.isFolder && handleNoteSelect(note.id)}
              className={cn(
                "flex items-center gap-2 px-2 py-1.5 text-sm hover:bg-secondary/50 rounded-sm cursor-pointer",
                isNoteActive(note.id) && "bg-secondary"
              )}
            >
              {getNoteIcon(note)}
              <span className="truncate flex-1">{note.title || "Untitled"}</span>
              {!isCollapsed && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-5 w-5 p-0 text-muted-foreground opacity-0 group-hover:opacity-100"
                    >
                      <MoreHorizontal className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" side="right" className="w-48">
                    {note.isFolder && (
                      <>
                        <DropdownMenuItem onClick={() => handleCreateNote(note.id)}>
                          <FileText className="mr-2 h-3.5 w-3.5" /> New Note
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openFolderDialog(note.id)}>
                          <Folder className="mr-2 h-3.5 w-3.5" /> New Folder
                        </DropdownMenuItem>
                      </>
                    )}
                    <DropdownMenuItem
                      onClick={() => handleDeleteItem(note)}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="mr-2 h-3.5 w-3.5" /> Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          ))}
        </div>
      );
    }

    return (
      <Tree
        className="relative"
        indent={20}
        tree={tree}
      >
        <AssistiveTreeDescription tree={tree} />
        {tree.getItems().map((item) => {
          const noteData = item.getItemData();
          if (!noteData || item.getId() === 'root') return null;

          return (
            <TreeItem key={item.getId()} item={item} className="group">
              <TreeItemLabel
                className="rounded-sm py-1.5 px-2 hover:bg-secondary/50"
                onClick={() => !noteData.isFolder && handleNoteSelect(item.getId())}
              >
                <span className="flex items-center gap-2 w-full">
                  {getNoteIcon(noteData)}
                  <span className="truncate flex-1">{noteData.title || "Untitled"}</span>
                  {!isCollapsed && !noteData.isFolder && noteData.createdAt && (
                    <span className="text-xs text-muted-foreground">
                      {formatDate(noteData.createdAt)}
                    </span>
                  )}
                  {!isCollapsed && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-5 w-5 p-0 text-muted-foreground opacity-0 group-hover:opacity-100"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" side="right" className="w-48">
                        {noteData.isFolder && (
                          <>
                            <DropdownMenuItem onClick={() => handleCreateNote(item.getId())}>
                              <FileText className="mr-2 h-3.5 w-3.5" /> New Note
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openFolderDialog(item.getId())}>
                              <Folder className="mr-2 h-3.5 w-3.5" /> New Folder
                            </DropdownMenuItem>
                          </>
                        )}
                        <DropdownMenuItem
                          onClick={() => handleDeleteItem(noteData)}
                          className="text-destructive focus:text-destructive"
                        >
                          <Trash2 className="mr-2 h-3.5 w-3.5" /> Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </span>
              </TreeItemLabel>
            </TreeItem>
          );
        })}
      </Tree>
    );
  };

  // Render loading skeleton - more compact version
  const renderSkeleton = () => (
    <div className="px-3 py-1.5 space-y-1.5">
      <div className="flex items-center gap-2">
        <Skeleton className="h-3.5 w-3.5 rounded-sm" />
        <Skeleton className="h-3.5 w-[80%] rounded-sm" />
      </div>
      <div className="flex items-center gap-2 ml-3">
        <Skeleton className="h-3.5 w-3.5 rounded-sm" />
        <Skeleton className="h-3.5 w-[70%] rounded-sm" />
      </div>
      <div className="flex items-center gap-2">
        <Skeleton className="h-3.5 w-3.5 rounded-sm" />
        <Skeleton className="h-3.5 w-[60%] rounded-sm" />
      </div>
      <div className="flex items-center gap-2 ml-3">
        <Skeleton className="h-3.5 w-3.5 rounded-sm" />
        <Skeleton className="h-3.5 w-[50%] rounded-sm" />
      </div>
      <div className="flex items-center gap-2 ml-3">
        <Skeleton className="h-3.5 w-3.5 rounded-sm" />
        <Skeleton className="h-3.5 w-[45%] rounded-sm" />
      </div>
    </div>
  );

  // Add this function to confirm deletion
  const confirmDelete = async () => {
    if (!itemToDelete) return;

    toast.promise(
      deleteNote(itemToDelete.id),
      {
        loading: `Deleting ${itemToDelete.isFolder ? 'folder' : 'note'}...`,
        success: (result) => {
          if (result.success) {
            fetchNotes(); // Refresh the notes list
            return `${itemToDelete.isFolder ? 'Folder' : 'Note'} deleted successfully`;
          } else {
            throw new Error(result.error);
          }
        },
        error: (err) => `Failed to delete: ${err.message || 'Unknown error'}`
      }
    );

    setIsDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  return (
    <>
      <div className={cn(
        "note-sidebar",
        isCollapsed && "collapsed",
        "rounded-tl-lg rounded-bl-lg"
      )}>
        <div className="note-sidebar-header rounded-tl-lg">
          {!isCollapsed && (
            <h3 className="note-sidebar-header-text">Notes</h3>
          )}
          <button 
            className="note-sidebar-toggle ml-auto"
            onClick={toggleSidebar}
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {isCollapsed ? <ChevronRight className="h-3.5 w-3.5" /> : <ChevronLeft className="h-3.5 w-3.5" />}
          </button>
        </div>
        
        {!isCollapsed && (
          <div className="note-sidebar-search">
            <div className="search-input-container">
              <Input
                placeholder="Search notes..."
                className="search-input"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Search className="search-icon" />
            </div>
          </div>
        )}
        
        <div className="note-sidebar-content">
          {isLoading ? (
            renderSkeleton()
          ) : notes.length === 0 ? (
            <div className="sidebar-empty">
              {!isCollapsed && "No notes found"}
            </div>
          ) : (
            <>
              {/* Published Notes Section */}
              {!isCollapsed && publishedNotes.length > 0 && (
                <div className="sidebar-section">
                  <div className="section-header">
                    <Globe2 className="h-3.5 w-3.5" />
                    <span>Published</span>
                    <span className="count-badge">{publishedNotes.length}</span>
                  </div>
                  <div className="section-content">
                    {publishedNotes.slice(0, 3).map(note => (
                      <Link
                        key={note.id}
                        href={`/hr/workspace/note/${note.id}`}
                        className={cn(
                          "note-item",
                          isNoteActive(note.id) && "note-item-active"
                        )}
                      >
                        <FileText className="h-3.5 w-3.5 text-green-400" />
                        <span className="truncate">{note.title || "Untitled"}</span>
                      </Link>
                    ))}
                  </div>
                  <Separator className="my-2 opacity-50" />
                </div>
              )}
              
              {/* Recent Notes Section */}
              {!isCollapsed && recentNotes.length > 0 && (
                <div className="sidebar-section">
                  <div className="section-header">
                    <Clock className="h-3.5 w-3.5" />
                    <span>Recent</span>
                  </div>
                  <div className="section-content">
                    {recentNotes.slice(0, 3).map(note => (
                      <Link
                        key={note.id}
                        href={`/hr/workspace/note/${note.id}`}
                        className={cn(
                          "note-item",
                          isNoteActive(note.id) && "note-item-active"
                        )}
                      >
                        <FileText className="h-3.5 w-3.5 text-muted-foreground" />
                        <span className="truncate">{note.title || "Untitled"}</span>
                      </Link>
                    ))}
                  </div>
                  <Separator className="my-2 opacity-50" />
                </div>
              )}
              
              {/* All Notes Section */}
              <div className="sidebar-section">
                <div className="section-header">
                  <Folder className="h-3.5 w-3.5" />
                  <span>Workspace</span>
                  <div className="ml-auto">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="workspace-add-button">
                          <Plus className="h-3.5 w-3.5" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem onClick={() => handleCreateNote()}>
                          <FileText className="mr-2 h-3.5 w-3.5" /> New Note
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openFolderDialog()}>
                          <Folder className="mr-2 h-3.5 w-3.5" /> New Folder
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                <div className="tree-container">
                  {notes.length === 0 && !searchQuery.trim() && (
                    <div className="empty-workspace-placeholder">
                      <p className="text-xs text-muted-foreground text-center py-4">
                        No items in workspace root
                      </p>
                    </div>
                  )}

                  {renderNewTree()}
                </div>
              </div>
            </>
          )}
        </div>
        
        {!isCollapsed && (
          <div className="note-sidebar-footer rounded-bl-lg">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="secondary" className="new-note-button">
                  <Plus className="new-note-icon" />
                  New Document
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => handleCreateNote()}>
                  <FileText className="mr-2 h-3.5 w-3.5" /> New Note
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => openFolderDialog()}>
                  <Folder className="mr-2 h-3.5 w-3.5" /> New Folder
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
        
        {isCollapsed && (
          <div className="note-sidebar-footer collapsed rounded-bl-lg">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="new-note-icon-button">
                  <Plus className="h-3.5 w-3.5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" side="right" className="w-48">
                <DropdownMenuItem onClick={() => handleCreateNote()}>
                  <FileText className="mr-2 h-3.5 w-3.5" /> New Note
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => openFolderDialog()}>
                  <Folder className="mr-2 h-3.5 w-3.5" /> New Folder
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>
      
      {/* Modal Dialog for Folder Creation */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {parentFolderId ? "Create Subfolder" : "Create Folder"}
            </DialogTitle>
            <DialogDescription>
              Enter a name for your new folder.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Input
                id="folder-name"
                placeholder="Folder name"
                className="col-span-4"
                value={folderName}
                onChange={(e) => setFolderName(e.target.value)}
                autoFocus
      />
    </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateFolder}>
              <Save className="mr-2 h-4 w-4" />
              Create Folder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              {itemToDelete?.isFolder 
                ? "This will delete the folder and all its contents. This action cannot be undone."
                : "This will permanently delete this note. This action cannot be undone."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}