'use server'

import { auth } from "@clerk/nextjs/server"
import prisma from "@/lib/db"
import { eachDayOfInterval, format } from 'date-fns'

interface ActivityData {
  date: string;
  count: number;
}

interface ActivityResponse {
  success: boolean;
  data?: {
    notes: ActivityData[];
    workflows: ActivityData[];
    chats: ActivityData[];
  };
  error?: string;
  hasActivity: boolean;
}

export async function getActivityData(): Promise<ActivityResponse> {
  try {
    const { userId: clerkId } = auth()
    if (!clerkId) {
      return { success: false, error: "Unauthorized", hasActivity: false };
    }

    // First get the user's internal ID from their clerkId
    const user = await prisma.user.findUnique({
      where: { clerkId },
      select: { id: true }
    });

    if (!user) {
      return { success: false, error: "User not found", hasActivity: false };
    }

    // Get the date range (last 30 days)
    const endDate = new Date()
    const startDate = new Date(endDate)
    startDate.setDate(startDate.getDate() - 30)

    // Get all items within date range for the specific user
    const [notes, workflows, channels] = await Promise.all([
      prisma.note.findMany({
        where: { 
          userId: user.id, // Use internal userId
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        select: { createdAt: true },
        orderBy: { createdAt: 'asc' }
      }),
      prisma.diagram.findMany({
        where: { 
          clerkId, // Use clerkId directly for diagrams
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        select: { createdAt: true },
        orderBy: { createdAt: 'asc' }
      }),
      prisma.channel.findMany({
        where: { 
          creatorId: user.id, // Use internal userId
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        select: { createdAt: true },
        orderBy: { createdAt: 'asc' }
      })
    ]);

    // Check if user has any activity
    const hasActivity = notes.length > 0 || workflows.length > 0 || channels.length > 0;

    // If no activity, return early
    if (!hasActivity) {
      return {
        success: true,
        data: {
          notes: [],
          workflows: [],
          chats: []
        },
        hasActivity: false
      };
    }

    // Generate daily counts
    const days = eachDayOfInterval({ start: startDate, end: endDate })
    
    const activityByDate = days.map(date => {
      const dateStr = format(date, 'yyyy-MM-dd')
      return {
        date: dateStr,
        notes: notes.filter(n => format(n.createdAt, 'yyyy-MM-dd') === dateStr).length,
        workflows: workflows.filter(w => format(w.createdAt, 'yyyy-MM-dd') === dateStr).length,
        chats: channels.filter(c => format(c.createdAt, 'yyyy-MM-dd') === dateStr).length
      }
    })

    // Convert to cumulative data
    let notesCount = 0
    let workflowsCount = 0
    let chatsCount = 0

    const cumulativeData = activityByDate.map(day => {
      notesCount += day.notes
      workflowsCount += day.workflows
      chatsCount += day.chats

      return {
        date: day.date,
        notes: notesCount,
        workflows: workflowsCount,
        chats: chatsCount
      }
    })

    return {
      success: true,
      data: {
        notes: cumulativeData.map(d => ({ date: d.date, count: d.notes })),
        workflows: cumulativeData.map(d => ({ date: d.date, count: d.workflows })),
        chats: cumulativeData.map(d => ({ date: d.date, count: d.chats }))
      },
      hasActivity: true
    }
  } catch (error) {
    console.error('Error fetching activity data:', error)
    return {
      success: false,
      error: 'Failed to fetch activity data',
      hasActivity: false
    }
  }
}
