"use client";

import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { 
  ChevronRight, 
  ChevronDown, 
  Folder, 
  FileText, 
  Plus, 
  ChevronLeft,
  Calendar,
  FolderOpen,
  Search,
  Clock,
  Globe2,
  Users,
  MoreHorizontal,
  Save,
  Trash2
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { getNotes, createFolder, updateNoteParent, deleteNote } from "@/actions/actions";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { format, formatDistanceToNow } from "date-fns";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function NoteClientSidebar() {
  const [notes, setNotes] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [folderName, setFolderName] = useState("");
  const [parentFolderId, setParentFolderId] = useState<string | undefined>(undefined);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [draggedItem, setDraggedItem] = useState<any | null>(null);
  const [dragOverItem, setDragOverItem] = useState<string | null>(null);
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  const [showDropIndicator, setShowDropIndicator] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<any | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  const pathname = usePathname();
  const router = useRouter();
  const dragTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    fetchNotes();
  }, []);

  useEffect(() => {
    if (draggedItem) {
      // Show helpful instruction toast when dragging begins
      const toastId = toast.info(
        draggedItem.isFolder 
          ? "Drop on another folder to move this folder" 
          : "Drop on a folder to move this note",
        {
          duration: 3000,
          id: "drag-instruction"
        }
      );
      
      return () => {
        toast.dismiss(toastId);
      };
    }
  }, [draggedItem]);

  const fetchNotes = async () => {
    setIsLoading(true);
    try {
      const response = await getNotes();
      // Check if the response was successful and contains notes
      if (response.success && response.notes) {
        setNotes(response.notes);
        
        // Auto-expand parent folders of active note
        if (pathname.includes('/note/')) {
          const noteId = pathname.split('/').pop();
          const note = response.notes.find((n: any) => n.id === noteId);
          if (note && note.parentId) {
            setExpandedFolders(prev => ({
              ...prev,
              [note.parentId as string]: true
            }));
          }
        }
      } else {
        setNotes([]);
      }
    } catch (error) {
      console.error("Failed to fetch notes", error);
      setNotes([]);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleFolder = (folderId: string, e?: React.MouseEvent) => {
    e?.stopPropagation();
    setExpandedFolders((prev) => ({
      ...prev,
      [folderId]: !prev[folderId],
    }));
  };

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const isNoteActive = (noteId: string) => pathname === `/hr/workspace/note/${noteId}`;

  // Format date helper
  const formatDate = (date: string | Date) => {
    if (!date) return "";
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, 'MMM d, yyyy');
  };

  // Open folder creation dialog
  const openFolderDialog = (parentId?: string) => {
    setParentFolderId(parentId);
    setFolderName("");
    setIsDialogOpen(true);
  };

  // Create new folder
  const handleCreateFolder = async () => {
    if (!folderName.trim()) {
      toast.error("Folder name cannot be empty");
      return;
    }
    
    try {
      const result = await createFolder(folderName, parentFolderId);
      if (result.success) {
        toast.success(`Folder "${folderName}" created`);
        fetchNotes(); // Refresh notes
        
        // Expand parent folder if creating within a folder
        if (parentFolderId) {
          setExpandedFolders(prev => ({
            ...prev,
            [parentFolderId]: true
          }));
        }
        
        setIsDialogOpen(false);
      } else {
        toast.error(result.error || 'Failed to create folder');
      }
    } catch (error) {
      console.error('Error creating folder:', error);
      toast.error('An error occurred while creating folder');
    }
  };

  // Create new note inside a folder
  const handleCreateNote = (parentId?: string) => {
    router.push('/hr/workspace/note' + (parentId ? `?parentId=${parentId}` : ''));
  };

  // Handle drag and drop
  const handleDragStart = (e: React.DragEvent, item: any) => {
    e.stopPropagation();
    
    // Force application of class with stronger approach
    e.currentTarget.classList.add('dragging');
    
    // Set data transfer for compatibility
    e.dataTransfer.setData('text/plain', item.id);
    e.dataTransfer.effectAllowed = 'move';
    
    // Store reference to the dragged item
    setDraggedItem(item);
    
    // Apply dragging class to all elements that match the id
    document.querySelectorAll(`[data-id="${item.id}"]`).forEach(el => {
      el.classList.add('dragging');
    });
  };

  const handleDragEnd = (e: React.DragEvent) => {
    // Remove the dragging class from all elements
    document.querySelectorAll('.dragging').forEach(el => {
      el.classList.remove('dragging');
    });
    
    setDraggedItem(null);
    setDragOverItem(null);
    setIsDraggingOver(false);
    
    if (dragTimeout.current) {
      clearTimeout(dragTimeout.current);
      dragTimeout.current = null;
    }
  };

  const handleDragOver = (e: React.DragEvent, item: any) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Only allow dropping on folders
    if (!item.isFolder || draggedItem?.id === item.id) {
      e.dataTransfer.dropEffect = 'none';
      return;
    }
    
    e.dataTransfer.dropEffect = 'move';
    
    if (dragOverItem !== item.id) {
      setDragOverItem(item.id);
      
      // Auto-expand folder after hovering for a moment
      if (dragTimeout.current) {
        clearTimeout(dragTimeout.current);
      }
      
      dragTimeout.current = setTimeout(() => {
        if (!expandedFolders[item.id]) {
          setExpandedFolders(prev => ({
            ...prev,
            [item.id]: true
          }));
        }
      }, 800);
    }
  };

  const handleDrop = async (e: React.DragEvent, targetItem: any) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (dragTimeout.current) {
      clearTimeout(dragTimeout.current);
      dragTimeout.current = null;
    }
    
    // Clear drag states
    setDraggedItem(null);
    setDragOverItem(null);
    
    if (!draggedItem || !targetItem.isFolder) return;
    
    // Prevent dropping on itself or dropping a parent folder into its own child
    if (draggedItem.id === targetItem.id) return;
    
    // Check if target is a child of the dragged item (to prevent circular nesting)
    let current = targetItem;
    while (current && current.parentId) {
      if (current.parentId === draggedItem.id) {
        toast.error("Cannot move a folder into its own subfolder");
        return;
      }
      current = notes.find(n => n.id === current.parentId);
    }

    // Prevent unnecessary DB updates if dropping to the same parent
    if (draggedItem.parentId === targetItem.id) {
      toast.info("Item is already in this folder");
      return;
    }
    
    try {
      toast.promise(
        updateNoteParent(draggedItem.id, targetItem.id),
        {
          loading: `Moving ${draggedItem.isFolder ? 'folder' : 'note'} to ${targetItem.title}...`,
          success: (result) => {
            if (result.success) {
              // Refresh the notes list to show the updated structure
              fetchNotes();
              
              // Auto-expand the target folder to show the moved item
              setExpandedFolders(prev => ({
                ...prev,
                [targetItem.id]: true
              }));
              
              return `Moved ${draggedItem.isFolder ? 'folder' : 'note'} successfully`;
      } else {
              throw new Error(result.error);
            }
          },
          error: (err) => `Failed to move item: ${err.message}`
      }
      );
    } catch (error) {
      console.error('Error moving item:', error);
      toast.error('An error occurred while moving the item');
    }
  };

  const handleDragLeave = () => {
    if (dragTimeout.current) {
      clearTimeout(dragTimeout.current);
      dragTimeout.current = null;
    }
    setDragOverItem(null);
  };

  // Get published notes
  const publishedNotes = notes.filter(note => note.isPublished);
  
  // Get recent notes (last 7 days, non-folders)
  const recentNotes = notes
    .filter(note => !note.isFolder && new Date(note.updatedAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, 5);
  
  // Filter for root-level items (no parent)
  const rootItems = notes.filter((note) => !note.parentId);
  
  // Filter by search query
  const filteredItems = searchQuery.trim() === "" 
    ? rootItems
    : notes.filter(note => 
        (note.title?.toLowerCase().includes(searchQuery.toLowerCase()) || 
         (typeof note.content === 'string' && note.content.toLowerCase().includes(searchQuery.toLowerCase())))
      );

  // Recursive function to render the file tree
  const renderFileTree = (items: any[]) => {
    return items.map((item) => {
      const isActive = isNoteActive(item.id);
      const isFolder = item.isFolder === true;
      const isExpanded = expandedFolders[item.id] || false;
      const childNotes = notes.filter((note) => note.parentId === item.id);
      const isPublished = item.isPublished;
      const isDragOver = dragOverItem === item.id;
      
      // Parse metadata for collaborative info
      let isCollaborative = false;
      try {
        if (item.metadata) {
          const metadata = typeof item.metadata === 'string'
            ? JSON.parse(item.metadata)
            : item.metadata;
          isCollaborative = !!metadata.collaborationId;
        }
      } catch (e) {
        console.error('Error parsing metadata:', e);
      }

      // Skip items that don't match search (when searching)
      if (searchQuery.trim() !== "" && !isFolder && 
          !item.title?.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !(typeof item.content === 'string' && item.content.toLowerCase().includes(searchQuery.toLowerCase()))) {
        const hasMatchingChildren = childNotes.some(child => 
          child.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (typeof child.content === 'string' && child.content.toLowerCase().includes(searchQuery.toLowerCase()))
        );
        if (!hasMatchingChildren) return null;
      }

      return (
        <div 
          key={item.id} 
          data-id={item.id}
          className={cn(
            "tree-item-container group",
            isDragOver && "drag-over"
          )}
          draggable
          onDragStart={(e) => handleDragStart(e, item)}
          onDragEnd={handleDragEnd}
          onDragOver={(e) => handleDragOver(e, item)}
          onDrop={(e) => handleDrop(e, item)}
          onDragLeave={handleDragLeave}
        >
          <div
            className={cn(
              "tree-item",
              isActive && "tree-item-active",
              isFolder && "tree-item-folder"
            )}
            onClick={isFolder ? () => toggleFolder(item.id) : undefined}
          >
            <div className="tree-item-icon-container">
              {isFolder ? (
                isExpanded ? (
                  <FolderOpen className="tree-folder-icon" />
                ) : (
                  <Folder className="tree-folder-icon" />
                )
              ) : isCollaborative ? (
                <Users className="text-blue-400 h-3.5 w-3.5" />
              ) : isPublished ? (
                <Globe2 className="text-green-400 h-3.5 w-3.5" />
              ) : (
                <FileText className="tree-file-icon" />
              )}
            </div>
            
            <div className="tree-item-content">
              {isFolder ? (
                <span className="tree-item-title">
                  {item.title || "Untitled Folder"}
                </span>
              ) : (
                <Link
                  href={`/hr/workspace/note/${item.id}`}
                  className="tree-item-title"
                >
                  {item.title || "Untitled"}
                </Link>
              )}
              
              {!isCollapsed && !isFolder && item.createdAt && (
                <span className="tree-item-date">
                  {formatDate(item.createdAt)}
                </span>
              )}
            </div>
            
            {!isCollapsed && (
              <div className="flex items-center opacity-0 group-hover:opacity-100">
                {/* Context menu for both files and folders */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-5 w-5 p-0 text-muted-foreground"
                    >
                      <MoreHorizontal className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" side="right" className="w-48">
                    {isFolder && (
                      <>
                        <DropdownMenuItem onClick={() => handleCreateNote(item.id)}>
                          <FileText className="mr-2 h-3.5 w-3.5" /> New Note
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openFolderDialog(item.id)}>
                          <Folder className="mr-2 h-3.5 w-3.5" /> New Folder
                        </DropdownMenuItem>
                      </>
                    )}
                    <DropdownMenuItem 
                      onClick={() => handleDeleteItem(item)}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="mr-2 h-3.5 w-3.5" /> Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                
                {!isCollapsed && isFolder && (
                  <button 
                    className="tree-folder-toggle"
                    onClick={(e) => toggleFolder(item.id, e)}
                  >
                    {isExpanded ? (
                      <ChevronDown className="h-3.5 w-3.5" />
                    ) : (
                      <ChevronRight className="h-3.5 w-3.5" />
                    )}
                  </button>
                )}
              </div>
            )}
          </div>
          
          {isFolder && isExpanded && childNotes.length > 0 && (
            <div className="tree-children">
              {renderFileTree(childNotes)}
            </div>
          )}
        </div>
      );
    }).filter(Boolean); // Filter out null items
  };

  // Render loading skeleton - more compact version
  const renderSkeleton = () => (
    <div className="px-3 py-1.5 space-y-1.5">
      <div className="flex items-center gap-2">
        <Skeleton className="h-3.5 w-3.5 rounded-sm" />
        <Skeleton className="h-3.5 w-[80%] rounded-sm" />
      </div>
      <div className="flex items-center gap-2 ml-3">
        <Skeleton className="h-3.5 w-3.5 rounded-sm" />
        <Skeleton className="h-3.5 w-[70%] rounded-sm" />
      </div>
      <div className="flex items-center gap-2">
        <Skeleton className="h-3.5 w-3.5 rounded-sm" />
        <Skeleton className="h-3.5 w-[60%] rounded-sm" />
      </div>
      <div className="flex items-center gap-2 ml-3">
        <Skeleton className="h-3.5 w-3.5 rounded-sm" />
        <Skeleton className="h-3.5 w-[50%] rounded-sm" />
      </div>
      <div className="flex items-center gap-2 ml-3">
        <Skeleton className="h-3.5 w-3.5 rounded-sm" />
        <Skeleton className="h-3.5 w-[45%] rounded-sm" />
      </div>
    </div>
  );

  // First, let's enhance the drag and drop mechanism to make it more intuitive

  // Update the workspace section to have a clear dedicated drop area
  const handleWorkspaceDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Only allow dropping if we have an item with a parent (coming from a folder)
    if (draggedItem && draggedItem.parentId) {
      setIsDraggingOver(true);
      e.dataTransfer.dropEffect = 'move';
    } else {
      e.dataTransfer.dropEffect = 'none';
    }
  };

  // Fix drag leave to correctly detect when mouse leaves the drop target
  const handleWorkspaceDragLeave = (e: React.DragEvent) => {
    const relatedTarget = e.relatedTarget as Node;
    // Only trigger when actually leaving the container, not entering children
    if (!e.currentTarget.contains(relatedTarget)) {
      setIsDraggingOver(false);
    }
  };

  // Simplify the RootDropZone component to be more straightforward
  const RootDropZone = () => (
    <div 
      className={cn(
        "root-drop-zone",
        isDraggingOver && "active",
        draggedItem?.parentId && "can-drop"
      )}
    >
      <FileText className="h-3.5 w-3.5 mr-1 inline-block" />
      <span>Drop here</span>
    </div>
  );

  // Make root tree items also capable of accepting drops next to them
  const handleRootItemDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (draggedItem && draggedItem.parentId) {
      e.dataTransfer.dropEffect = 'move';
      setIsDraggingOver(true);
    } else {
      e.dataTransfer.dropEffect = 'none';
    }
  };

  // Update the tree-container rendering to include the drop zone
  // and make tree items also accept drops:

  const handleWorkspaceDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDraggingOver(false);
    
    // Early exit if no item is being dragged
    if (!draggedItem) {
      setDraggedItem(null);
      return;
    }
    
    // If item already has no parent, it's already at root
    if (!draggedItem.parentId) {
      toast.info("Item is already at the workspace root");
      setDraggedItem(null);
      return;
    }
    
    try {
      toast.promise(
        updateNoteParent(draggedItem.id, null),
        {
          loading: `Moving ${draggedItem.isFolder ? 'folder' : 'note'} to workspace root...`,
          success: (result) => {
            if (result.success) {
              // Clear drag state
              setDraggedItem(null);
              // Refresh the notes list
              fetchNotes();
              return `Moved to workspace root successfully`;
            } else {
              throw new Error(result.error);
            }
          },
          error: (err) => `Failed to move item to workspace root: ${err}`
        }
      );
    } catch (error) {
      console.error('Error moving item to root:', error);
      toast.error('An error occurred while moving the item');
      setDraggedItem(null);
    }
  };

  // Add this function to handle delete
  const handleDeleteItem = async (item: any) => {
    setItemToDelete(item);
    setIsDeleteDialogOpen(true);
  };

  // Add this function to confirm deletion
  const confirmDelete = async () => {
    if (!itemToDelete) return;
    
    toast.promise(
      deleteNote(itemToDelete.id),
      {
        loading: `Deleting ${itemToDelete.isFolder ? 'folder' : 'note'}...`,
        success: (result) => {
          if (result.success) {
            fetchNotes(); // Refresh the notes list
            return `${itemToDelete.isFolder ? 'Folder' : 'Note'} deleted successfully`;
          } else {
            throw new Error(result.error);
          }
        },
        error: (err) => `Failed to delete: ${err.message || 'Unknown error'}`
      }
    );
    
    setIsDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  return (
    <>
      <div className={cn(
        "note-sidebar",
        isCollapsed && "collapsed",
        "rounded-tl-lg rounded-bl-lg"
      )}>
        <div className="note-sidebar-header rounded-tl-lg">
          {!isCollapsed && (
            <h3 className="note-sidebar-header-text">Notes</h3>
          )}
          <button 
            className="note-sidebar-toggle ml-auto"
            onClick={toggleSidebar}
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {isCollapsed ? <ChevronRight className="h-3.5 w-3.5" /> : <ChevronLeft className="h-3.5 w-3.5" />}
          </button>
        </div>
        
        {!isCollapsed && (
          <div className="note-sidebar-search">
            <div className="search-input-container">
              <Input
                placeholder="Search notes..."
                className="search-input"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Search className="search-icon" />
            </div>
          </div>
        )}
        
        <div className="note-sidebar-content">
          {isLoading ? (
            renderSkeleton()
          ) : notes.length === 0 ? (
            <div className="sidebar-empty">
              {!isCollapsed && "No notes found"}
            </div>
          ) : (
            <>
              {/* Published Notes Section */}
              {!isCollapsed && publishedNotes.length > 0 && (
                <div className="sidebar-section">
                  <div className="section-header">
                    <Globe2 className="h-3.5 w-3.5" />
                    <span>Published</span>
                    <span className="count-badge">{publishedNotes.length}</span>
                  </div>
                  <div className="section-content">
                    {publishedNotes.slice(0, 3).map(note => (
                      <Link
                        key={note.id}
                        href={`/hr/workspace/note/${note.id}`}
                        className={cn(
                          "note-item",
                          isNoteActive(note.id) && "note-item-active"
                        )}
                      >
                        <FileText className="h-3.5 w-3.5 text-green-400" />
                        <span className="truncate">{note.title || "Untitled"}</span>
                      </Link>
                    ))}
                  </div>
                  <Separator className="my-2 opacity-50" />
                </div>
              )}
              
              {/* Recent Notes Section */}
              {!isCollapsed && recentNotes.length > 0 && (
                <div className="sidebar-section">
                  <div className="section-header">
                    <Clock className="h-3.5 w-3.5" />
                    <span>Recent</span>
                  </div>
                  <div className="section-content">
                    {recentNotes.slice(0, 3).map(note => (
                      <Link
                        key={note.id}
                        href={`/hr/workspace/note/${note.id}`}
                        className={cn(
                          "note-item",
                          isNoteActive(note.id) && "note-item-active"
                        )}
                      >
                        <FileText className="h-3.5 w-3.5 text-muted-foreground" />
                        <span className="truncate">{note.title || "Untitled"}</span>
                      </Link>
                    ))}
                  </div>
                  <Separator className="my-2 opacity-50" />
                </div>
              )}
              
              {/* All Notes Section */}
              <div className="sidebar-section">
                <div 
                  className={cn(
                    "section-header",
                    isDraggingOver && draggedItem?.parentId && "bg-accent/20 outline-dashed outline-1 outline-accent"
                  )}
                  onDragOver={handleWorkspaceDragOver}
                  onDragLeave={handleWorkspaceDragLeave}
                  onDrop={handleWorkspaceDrop}
                >
                  <Folder className="h-3.5 w-3.5" />
                  <span>Workspace</span>
                  <div className="ml-auto">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="workspace-add-button">
                          <Plus className="h-3.5 w-3.5" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem onClick={() => handleCreateNote()}>
                          <FileText className="mr-2 h-3.5 w-3.5" /> New Note
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openFolderDialog()}>
                          <Folder className="mr-2 h-3.5 w-3.5" /> New Folder
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
                
                <div 
                  className={cn(
                    "tree-container",
                    isDraggingOver && draggedItem?.parentId && "bg-accent/10" 
                  )}
                  onDragOver={handleWorkspaceDragOver}
                  onDragLeave={handleWorkspaceDragLeave}
                  onDrop={handleWorkspaceDrop}
                >
                  {rootItems.length === 0 && !searchQuery.trim() && (
                    <div className="empty-workspace-placeholder">
                      <p className="text-xs text-muted-foreground text-center py-4">
                        No items in workspace root
                      </p>
                    </div>
                  )}
                  
                  {renderFileTree(searchQuery.trim() !== "" ? filteredItems : rootItems)}
                </div>
              </div>
            </>
          )}
        </div>
        
        {!isCollapsed && (
          <div className="note-sidebar-footer rounded-bl-lg">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="secondary" className="new-note-button">
                  <Plus className="new-note-icon" />
                  New Document
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => handleCreateNote()}>
                  <FileText className="mr-2 h-3.5 w-3.5" /> New Note
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => openFolderDialog()}>
                  <Folder className="mr-2 h-3.5 w-3.5" /> New Folder
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
        
        {isCollapsed && (
          <div className="note-sidebar-footer collapsed rounded-bl-lg">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="new-note-icon-button">
                  <Plus className="h-3.5 w-3.5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" side="right" className="w-48">
                <DropdownMenuItem onClick={() => handleCreateNote()}>
                  <FileText className="mr-2 h-3.5 w-3.5" /> New Note
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => openFolderDialog()}>
                  <Folder className="mr-2 h-3.5 w-3.5" /> New Folder
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>
      
      {/* Modal Dialog for Folder Creation */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {parentFolderId ? "Create Subfolder" : "Create Folder"}
            </DialogTitle>
            <DialogDescription>
              Enter a name for your new folder.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Input
                id="folder-name"
                placeholder="Folder name"
                className="col-span-4"
                value={folderName}
                onChange={(e) => setFolderName(e.target.value)}
                autoFocus
      />
    </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateFolder}>
              <Save className="mr-2 h-4 w-4" />
              Create Folder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              {itemToDelete?.isFolder 
                ? "This will delete the folder and all its contents. This action cannot be undone."
                : "This will permanently delete this note. This action cannot be undone."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}