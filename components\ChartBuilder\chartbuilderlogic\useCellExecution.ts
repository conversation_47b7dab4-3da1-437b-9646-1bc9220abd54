import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Dataset } from '@/types/index';
import { UseCellExecutionReturn, CellData } from './types';
import { checkInternetConnection, initializeAlasqlUtils, isBrowser } from './chartBuilderUtils';

/**
 * Hook for handling cell execution in the ChartBuilder
 * @param setCells Function to update cells state
 * @param cells Current cells state
 * @param datasetCache Dataset cache for quick access
 * @returns Cell execution functions and state
 */
export const useCellExecution = (
  setCells: React.Dispatch<React.SetStateAction<CellData[]>>,
  cells: CellData[],
  datasetCache: Record<string, Dataset>
): UseCellExecutionReturn => {
  const [isAlasqlInitialized, setIsAlasqlInitialized] = useState(false);
  const [currentData, setCurrentData] = useState<any[]>([]);

  // Initialize alasql-utils on component mount
  useEffect(() => {
    initializeAlasqlUtils().then(initialized => {
      setIsAlasqlInitialized(initialized);
    });
  }, []);

  // Execute cell code
  const handleRunCell = async (cellId: string, code: string, showGraphicWalker: boolean = false) => {
    try {
      const cell = cells.find(c => c.id === cellId);
      if (!cell) return;

      // Get the datasets for this specific cell
      const cellDatasetIds = cell.selectedDatasetIds || [];
      
      // For Python, allow execution without dataset selection (users can load their own data)
      if (cellDatasetIds.length === 0 && cell.language !== 'python') {
        toast.error('Please select at least one dataset for this cell first');
        return;
      }

      // Get the actual dataset objects for this cell from the cache
      const cellDatasets = cellDatasetIds
        .map(id => datasetCache[id])
        .filter(ds => ds !== undefined);

      // Add loading state to specific cell
      setCells(prev => prev.map(c =>
        c.id === cellId ? { ...c, isLoading: true } : c
      ));

      let endpoint: string;
      let payload: any;
      let response: Response | undefined;

      // Check for GraphicWalker or loopchart commands
      const hasVisualizationCommand = cell.language === 'sql' &&
        (code.includes("--#graphicwalker") ||
         code.includes("-- #graphicwalker") ||
         code.includes("--loopchart") ||
         code.includes("-- loopchart"));

      // Set the showGraphicWalker flag based on command or explicit parameter
      const shouldShowGraphicWalker = showGraphicWalker || hasVisualizationCommand;

      const startTime = Date.now();

      // Check for internet connection to determine execution strategy
      const isOnline = await checkInternetConnection();

      if (!isOnline && cell.language !== 'sql') {
        throw new Error(`${cell.language} execution requires internet connection. Only SQL is available offline.`);
      }

      switch (cell.language) {
        case 'javascript':
          endpoint = '/api/execute-js';
          payload = {
            code,
            datasets: cellDatasets.map(ds => ({
              id: ds.id,
              name: ds.name,
              data: ds.data
            })),
          };
          response = await fetch(endpoint, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
          });
          break;
        case 'python':
          // Use the API
          endpoint = '/api/execute';
          payload = {
            code,
            datasetId: cellDatasets.length > 0 ? cellDatasets[0].id : '', // Keep for backward compatibility
            datasetIds: cellDatasets.map(ds => ds.id), // Send all selected dataset IDs
            datasets: cellDatasets.map(ds => ({ // Send actual dataset data
              id: ds.id,
              name: ds.name,
              data: ds.data
            })),
            language: 'python'
          };
          console.log('Sending Python payload with multiple datasets:', payload);

          try {
            response = await fetch(endpoint, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(payload)
            });
          } catch (error) {
            console.error('Python API call failed:', error);
            toast.error('Failed to execute Python code. Please try again.');
            throw new Error('Failed to execute Python code');
          }
          break;
        case 'sql':
          // Always use client-side execution in offline mode
          if (!isOnline) {
            // Skip server-side attempt in offline mode
            if (isBrowser) {
              try {
                // Load alasql-utils dynamically
                const module = await import('@/lib/alasql-utils');
                const { setupTable, executeQuery } = module;

                // Setup each dataset with its actual name
                cellDatasets.forEach((ds) => {
                  // Create a safe table name by removing file extension and special characters
                  const safeTableName = ds.name
                    .replace(/\.[^/.]+$/, '') // Remove file extension
                    .toLowerCase()
                    .replace(/[^a-z0-9]/g, '_'); // Replace special chars with underscore
                  
                  // Transform MongoDB data to ensure proper column structure
                  const transformedData = ds.data.map((row: any) => {
                    const newRow: any = {};
                    Object.entries(row).forEach(([key, value]) => {
                      // Handle nested MongoDB fields
                      if (typeof value === 'object' && value !== null) {
                        Object.entries(value).forEach(([nestedKey, nestedValue]) => {
                          newRow[`${key}_${nestedKey}`] = nestedValue;
                        });
                      } else {
                        newRow[key] = value;
                      }
                    });
                    return newRow;
                  });
                  
                  setupTable(safeTableName, transformedData);
                });

                // Execute the query
                let result = executeQuery(code);

                // Handle UNION queries by ensuring column alignment
                if (code.toLowerCase().includes('union')) {
                  // Get all column names from the result
                  const allColumns = new Set<string>();
                  result.forEach(row => {
                    Object.keys(row).forEach(col => allColumns.add(col));
                  });

                  // Ensure all rows have the same columns and are properly aligned
                  result = result.map(row => {
                    const newRow: any = {};
                    allColumns.forEach(col => {
                      newRow[col] = row[col] ?? null;
                    });
                    return newRow;
                  });
                }

                // Create a mock response
                response = {
                  ok: true,
                  json: async () => ({
                    data: result,
                    output: `Query executed successfully (client-side). Returned ${result.length} rows.`
                  })
                } as Response;
              } catch (sqlError: any) {
                throw new Error(`SQL Error: ${sqlError.message}`);
              }
            } else {
              // If we're on the server, we need to handle this case
              throw new Error('SQL execution not available in server environment');
            }
          } else {
            // Try server-side first, then fall back to client-side
            endpoint = '/api/query';
            payload = {
              query: code.trim(),
              datasets: cellDatasets.map(ds => ({
                id: ds.id,
                name: ds.name,
                data: ds.data
              })),
            };
            console.log('Sending SQL payload with multiple datasets:', payload);

            try {
              // First try the server-side approach
              response = await fetch(endpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
              });

              if (!response.ok) {
                throw new Error('Server-side execution failed');
              }
            } catch (error) {
              console.warn('Server-side SQL execution failed, falling back to client-side AlasQL:', error);

              // Inside client-side fallback for SQL execution, check for browser environment
              if (isBrowser) {
                try {
                  // Load alasql-utils dynamically
                  const module = await import('@/lib/alasql-utils');
                  const { setupTable, executeQuery } = module;

                  // Setup each dataset with its actual name
                  cellDatasets.forEach((ds) => {
                    // Create a safe table name by removing file extension and special characters
                    const safeTableName = ds.name
                      .replace(/\.[^/.]+$/, '') // Remove file extension
                      .toLowerCase()
                      .replace(/[^a-z0-9]/g, '_'); // Replace special chars with underscore
                    
                    // Transform MongoDB data to ensure proper column structure
                    const transformedData = ds.data.map((row: any) => {
                      const newRow: any = {};
                      Object.entries(row).forEach(([key, value]) => {
                        // Handle nested MongoDB fields
                        if (typeof value === 'object' && value !== null) {
                          Object.entries(value).forEach(([nestedKey, nestedValue]) => {
                            newRow[`${key}_${nestedKey}`] = nestedValue;
                          });
                        } else {
                          newRow[key] = value;
                        }
                      });
                      return newRow;
                    });
                    
                    setupTable(safeTableName, transformedData);
                  });

                  // Execute the query
                  let result = executeQuery(code);

                  // Handle UNION queries by ensuring column alignment
                  if (code.toLowerCase().includes('union')) {
                    // Get all column names from the result
                    const allColumns = new Set<string>();
                    result.forEach(row => {
                      Object.keys(row).forEach(col => allColumns.add(col));
                    });

                    // Ensure all rows have the same columns and are properly aligned
                    result = result.map(row => {
                      const newRow: any = {};
                      allColumns.forEach(col => {
                        newRow[col] = row[col] ?? null;
                      });
                      return newRow;
                    });
                  }

                  // Create a mock response
                  response = {
                    ok: true,
                    json: async () => ({
                      data: result,
                      output: `Query executed successfully (client-side). Returned ${result.length} rows.`
                    })
                  } as Response;
                } catch (sqlError: any) {
                  throw new Error(`SQL Error: ${sqlError.message}`);
                }
              } else {
                // If we're on the server, we need to handle this case
                throw new Error('SQL execution not available in server environment');
              }
            }
          }
          break;
        default:
          throw new Error(`Unsupported language: ${cell.language}`);
      }

      // Check if response is defined before using it
      if (!response) {
        throw new Error(`No response received for ${cell.language} execution`);
      }

      // Now we can safely use response since we've checked it exists
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to execute ${cell.language} code`);
      }

      const executionTime = Date.now() - startTime;

      // Only read the response body ONCE
      const responseData = await response.json();

      // Check for error in responseData
      if (responseData.error) {
        setCells(prev => prev.map(c =>
          c.id === cellId
            ? {
                ...c,
                error: responseData.error,
                errorDetails: responseData.errorDetails,
                executionTime: responseData.executionTime || executionTime,
                isSuccess: false,
                isLoading: false
              }
            : c
        ));
        return;
      }

      // Standardize the result format
      const processedData = Array.isArray(responseData) ? responseData :
                          responseData.data ? responseData.data :
                          [responseData];

      // Success case - Fix structure to match CellProps result type
      setCells(prev => prev.map(c =>
        c.id === cellId
          ? {
              ...c,
              result: {
                data: processedData,
                output: responseData.output,
                plots: responseData.plots || [], // Ensure plots is always an array
              },
              error: undefined,
              errorDetails: undefined,
              executionTime: responseData.executionTime || executionTime,
              isSuccess: true,
              isLoading: false,
              showGraphicWalker: shouldShowGraphicWalker || responseData.showGraphicWalker
            }
          : c
      ));

      setCurrentData(processedData);
      toast.success(`${cell.language} executed successfully`);

    } catch (error) {
      console.error('Execution error:', error);
      toast.error(error instanceof Error ? error.message : 'Execution failed');

      // Update cell with error
      setCells(prev => prev.map(c =>
        c.id === cellId
          ? { 
              ...c, 
              error: error instanceof Error ? error.message : 'Execution failed', 
              isSuccess: false,
              isLoading: false 
            }
          : c
      ));
    }
  };

  return {
    handleRunCell,
    isAlasqlInitialized
  };
};
