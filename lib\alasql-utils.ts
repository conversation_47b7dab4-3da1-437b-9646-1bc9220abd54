// Using direct import of the alasql core
import alasql from 'alasql/dist/alasql.min.js';

// Type definitions for alasql
interface AlasqlTable {
  data: any[];
}

interface AlasqlTables {
  [tableName: string]: AlasqlTable;
}

interface Alasql {
  (sql: string, params?: any[]): any;
  tables: AlasqlTables;
}

// Cast alasql to our interface
const alasqlTyped = alasql as unknown as Alasql;

/**
 * Sets up a table with data in AlasQL
 * @param tableName The name of the table
 * @param data Array of objects to load
 */
export function setupTable(tableName: string, data: any[]): void {
  // Create the table if it doesn't exist
  alasqlTyped(`CREATE TABLE IF NOT EXISTS ${tableName}`);
  
  // Clear existing data
  alasqlTyped(`DELETE FROM ${tableName}`);
  
  // Set the data directly
  alasqlTyped.tables[tableName] = { data: [...data] };
}

/**
 * Execute a SQL query using AlasQL
 * @param query The SQL query to execute
 * @param params Optional parameters for the query
 * @returns The result of the query
 */
export function executeQuery(query: string, params?: any[]): any[] {
  try {
    return alasqlTyped(query, params);
  } catch (error) {
    console.error('AlasQL error:', error);
    throw error;
  }
} 