/* Create this file and import it in your main CSS file or app layout */
/* Main container structure */
.blocknote-container {
  height: 100%;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding-top: 0; /* Ensure no extra padding pushes content down */
}

/* Hide scrollbar for Chrome/Safari/Opera */
.blocknote-container::-webkit-scrollbar {
  display: none;
}

/* Content sizing */
.blocknote-content-wrapper {
  width: 100%;
  max-width: 100%; /* Use full width */
  margin: 0 auto;
  padding: 0 32px 120px;
  padding-top: 16px; /* Add some space below the header */
  transition: max-width 0.3s ease, width 0.3s ease; /* Smooth transition when sidebar changes */
}

/* Ensure content is centered in all sidebar states */
.editor-container.sidebar-closed .blocknote-content-wrapper,
.editor-container.sidebar-open .blocknote-content-wrapper,
.editor-container.full-width .blocknote-content-wrapper {
  margin-left: auto;
  margin-right: auto;
}

/* BlockNote styling overrides */
.bn-container {
  border: none !important;
  overflow: visible !important;
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  margin: 0 auto !important; /* Center the container */
  max-width: 100% !important; /* Use full width */
  width: 100% !important; /* Ensure full width */
}

.bn-editor {
  overflow: visible !important;
  height: auto !important;
  min-height: 70vh;
  width: 100% !important;
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
}

/* Any other BlockNote container that might create scroll */
.bn-editor-container, .bn-container-block {
  overflow: visible !important;
}

/* ===== HEADER STYLING ===== */
/* Fix header to work with the layout - not overlapping sidebar */
.header-note {
  position: sticky;
  top: 0;
  z-index: 50;
  padding: 8px 0;
  background-color: hsl(var(--background));
  backdrop-filter: blur(8px);
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s, box-shadow 0.2s;
  width: 100%; /* Full width of its parent */
}

.header-note.scrolled {
  border-bottom: 1px solid hsl(var(--border));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Adjust editor container to work with sticky header */
.editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  position: relative;
  width: 100%; /* Use full width */
  max-width: 100%; /* Allow container to take full width */
  transition: margin-left 0.3s ease, width 0.3s ease; /* Smooth transition when sidebar changes */
}

/* Editor container with sidebar open */
.editor-container.sidebar-open {
  margin-left: 0; /* Don't offset the container */
  width: 100%; /* Use full width */
}

/* Editor container with sidebar closed */
.editor-container.sidebar-closed {
  margin-left: 0; /* Don't offset the container */
  width: 100%; /* Use full width */
}

/* When in full-width mode (no sidebar) */
.editor-container.full-width {
  margin-left: 0;
  width: 100%;
}

/* Fix date info styling */
.note-date-info {
  font-size: 11px;
  color: hsl(var(--muted-foreground));
  padding-top: 4px;
  margin-top: 4px;
  border-top: 1px solid hsl(var(--border)/0.15);
}

/* Fix title input */
.header-note input.title-input {
  font-size: 1.5rem !important;
  line-height: 1.3 !important;
  font-weight: 600 !important;
  padding: 0 !important;
  height: auto !important;
  margin-bottom: 2px !important;
}

/* Button styling */
.header-note .btn-compact {
  height: 28px !important;
  min-height: 28px !important;
  padding-left: 8px !important;
  padding-right: 8px !important;
  font-size: 12px !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  letter-spacing: -0.01em !important;
}

.header-note .btn-compact svg {
  width: 13px !important;
  height: 13px !important;
  margin-right: 3px !important;
}

/* ===== LAYOUT CONTAINER ===== */
.note-layout-container {
  display: flex;
  height: 100vh; /* Full viewport height */
  width: 100%;
  position: relative;
  overflow: hidden;
}

/* Note content styling */
.note-content {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
  align-items: center; /* Center horizontally */
  width: 100%;
  height: 100%;
}

/* ===== SIDEBAR STYLING ===== */
.note-sidebar {
  flex-shrink: 0;
  position: sticky;
  top: 0;
  left: 0;
  height: 100vh; /* Full viewport height */
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  width: 260px; /* Slightly narrower */
  z-index: 60;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border-right: 1px solid hsl(var(--border));
}

.note-sidebar.collapsed {
  width: 48px; /* Smaller when collapsed */
}

/* Header section of sidebar */
.note-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px; /* Less padding */
  height: 40px; /* Shorter header */
  flex-shrink: 0;
  background-color: hsl(var(--background));
  border-bottom: 1px solid hsl(var(--border));
}

.note-sidebar-header-text {
  font-size: 0.875rem; /* Slightly smaller font */
  font-weight: 600;
  color: hsl(var(--foreground));
}

.note-sidebar-toggle {
  border: none;
  background: transparent;
  cursor: pointer;
  color: hsl(var(--muted-foreground));
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.note-sidebar-toggle:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* Search section */
.note-sidebar-search {
  padding: 6px 12px; /* Less padding */
  flex-shrink: 0;
  border-bottom: 1px solid hsl(var(--border));
  background-color: hsl(var(--background));
}

.search-input-container {
  position: relative;
  margin-bottom: 8px; /* Less margin */
}

.search-input {
  padding-left: 32px !important;
  font-size: 11px !important; /* Smaller font */
  height: 26px !important; /* Even smaller height */
  background-color: hsl(var(--secondary)) !important;
  border-color: hsl(var(--border)) !important;
}

.search-input:focus {
  border-color: hsl(var(--accent)) !important;
  box-shadow: 0 0 0 1px hsl(var(--ring));
}

.search-icon {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  color: hsl(var(--muted-foreground));
}

/* Section styling */
.sidebar-section {
  margin-bottom: 4px; /* Reduced margin */
}

.section-header {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px; /* Even smaller padding */
  font-size: 10px; /* Smaller font */
  font-weight: 600;
  color: hsl(var(--muted-foreground));
  letter-spacing: 0.02em; /* Slight letter spacing for readability */
  text-transform: uppercase; /* Make headers uppercase for distinction */
}

.count-badge {
  font-size: 8px; /* Smaller badge text */
  background-color: hsl(var(--accent)/0.2);
  color: hsl(var(--accent-foreground));
  border-radius: 9999px;
  padding: 0px 4px; /* Less padding */
  height: 14px; /* Fixed height */
  display: inline-flex;
  align-items: center;
}

.section-content {
  padding: 2px 0; /* Less padding */
}

.note-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 3px 12px; /* Smaller padding */
  font-size: 11px; /* Keep small font */
  color: hsl(var(--foreground));
  border-radius: 3px;
  margin: 0 4px;
  cursor: pointer;
  transition: background-color 0.15s;
  min-height: 22px; /* Match tree items */
}

.note-item:hover {
  background-color: hsl(var(--accent)/0.1);
}

.note-item-active {
  background-color: hsl(var(--accent)/0.15) !important;
  color: hsl(var(--accent-foreground));
}

/* Content section - where the file tree is */
.note-sidebar-content {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  background-color: hsl(var(--background));
}

.note-sidebar-content::-webkit-scrollbar {
  width: 4px; /* Thinner scrollbar */
}

.note-sidebar-content::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground)/0.3);
  border-radius: 8px;
}

.note-sidebar-content::-webkit-scrollbar-track {
  background-color: transparent;
}

/* Tree container */
.tree-container {
  padding: 4px 0;
  min-height: 50px; /* Ensure there's space to drop even when empty */
  position: relative;
}

/* Tree item styling - more compact */
.tree-item-container {
  margin: 0; /* Remove margin between items */
}

.tree-item {
  display: flex;
  align-items: center;
  padding: 3px 12px; /* Even smaller padding */
  cursor: pointer;
  border-radius: 3px;
  margin: 0 4px;
  position: relative;
  transition: background-color 0.15s ease;
  font-size: 11px; /* Already small font */
}

.tree-item:hover {
  background-color: hsl(var(--accent)/0.1);
}

.tree-item-active {
  background-color: hsl(var(--accent)/0.15) !important;
  color: hsl(var(--accent-foreground));
  font-weight: 500;
}

.tree-item-folder {
  font-weight: 500;
}

.tree-item-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px; /* Smaller */
  margin-right: 6px; /* Less margin */
  flex-shrink: 0;
}

.tree-folder-icon {
  width: 12px; /* Smaller icons */
  height: 12px; /* Smaller icons */
  color: hsl(var(--accent));
}

.tree-file-icon {
  width: 13px; /* Smaller */
  height: 13px; /* Smaller */
  color: hsl(var(--muted-foreground));
}

.tree-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* Enables truncation */
}

.tree-item-title {
  font-size: 11px; /* Smaller font */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tree-item-date {
  font-size: 9px; /* Smaller font */
  color: hsl(var(--muted-foreground));
  margin-top: 1px; /* Less margin */
}

.tree-folder-toggle {
  width: 16px; /* Smaller */
  height: 16px; /* Smaller */
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  color: hsl(var(--muted-foreground));
  cursor: pointer;
  padding: 0;
  margin-left: 3px; /* Less margin */
}

.tree-folder-toggle:hover {
  color: hsl(var(--foreground));
}

.tree-children {
  padding-left: 8px; /* Less indentation */
  position: relative;
  margin-left: 10px; /* Less margin */
  border-left: 1px dashed hsl(var(--border));
}

/* Loading and empty states */
.sidebar-loading, .sidebar-empty {
  padding: 12px;
  text-align: center;
  color: hsl(var(--muted-foreground));
  font-size: 11px; /* Smaller font */
}

/* Footer section */
.note-sidebar-footer {
  padding: 6px 12px; /* Less padding */
  border-top: 1px solid hsl(var(--border));
  background-color: hsl(var(--background));
}

.new-note-button {
  width: 100%;
  justify-content: center;
  font-size: 10px; /* Smaller font */
  height: 26px; /* Smaller height */
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  border: 1px solid hsl(var(--border));
}

.new-note-button:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.new-note-icon {
  margin-right: 6px; /* Less margin */
  width: 11px;
  height: 11px;
}

.note-sidebar-footer.collapsed {
  display: flex;
  justify-content: center;
  padding: 6px 0;
}

.new-note-icon-button {
  width: 28px; /* Smaller */
  height: 28px; /* Smaller */
  color: hsl(var(--muted-foreground));
}

/* Collapsed sidebar styling */
.collapsed .tree-item {
  padding: 8px 0; /* Smaller padding */
  justify-content: center;
}

.collapsed .tree-item-content,
.collapsed .tree-folder-toggle {
  display: none;
}

.collapsed .tree-item-icon-container {
  margin-right: 0;
}

.collapsed .tree-children {
  display: none;
}

/* Connect tree items with lines (VSCode style) */
.tree-children:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 1px;
  background-color: hsl(var(--border));
  z-index: 1;
}

.tree-item:before {
  content: "";
  position: absolute;
  top: 50%;
  left: -10px; /* Adjust for more compact sizing */
  width: 8px; /* Shorter line */
  height: 1px;
  background-color: hsl(var(--border));
  z-index: 1;
}

/* Don't show horizontal line for root level items */
.tree-container > .tree-item-container > .tree-item:before {
  display: none;
}

/* Improved tooltips for file info */
.group:hover .hidden.group-hover\:block {
  display: block;
}

.bg-popover {
  background-color: hsl(var(--popover));
  border: 1px solid hsl(var(--border));
}

/* Separator styling */
.opacity-50 {
  opacity: 0.5;
}

.my-2 {
  margin-top: 4px;
  margin-bottom: 4px;
}

/* Use shadcn variables for all UI elements */
.workspace-title {
  font-size: 11px; /* Smaller font */
  font-weight: 600;
  color: hsl(var(--foreground));
}

.workspace-add-button {
  height: 20px; /* Smaller */
  width: 20px; /* Smaller */
  padding: 0;
  color: hsl(var(--muted-foreground));
}

.workspace-add-button:hover {
  background-color: hsl(var(--accent)/0.1);
  color: hsl(var(--accent-foreground));
}

/* Note: Old drag and drop styles removed to prevent conflicts with react-flow and other components.
   The new implementation uses headless-tree which handles its own drag and drop styling. */

/* Make tree items more compact */
.tree-item {
  padding: 3px 12px; /* Even smaller padding */
  min-height: 22px; /* Set min-height for more compact look */
  font-size: 11px; /* Already small font */
}

.tree-folder-icon, .tree-file-icon {
  width: 12px; /* Smaller icons */
  height: 12px; /* Smaller icons */
}

.tree-item-container {
  margin: 0; /* Remove margin between items */
}

.tree-children {
  padding-left: 8px; /* Less indentation */
  margin-left: 10px; /* Less margin */
}

.section-header {
  padding: 4px 12px; /* Even smaller padding */
  font-size: 10px; /* Smaller font */
  letter-spacing: 0.02em; /* Slight letter spacing for readability */
  text-transform: uppercase; /* Make headers uppercase for distinction */
}

.note-item {
  padding: 3px 12px; /* Smaller padding */
  font-size: 11px; /* Keep small font */
  min-height: 22px; /* Match tree items */
}

.count-badge {
  font-size: 8px; /* Smaller badge text */
  padding: 0px 4px; /* Less padding */
  height: 14px; /* Fixed height */
  display: inline-flex;
  align-items: center;
}

/* More compact search */
.note-sidebar-search {
  padding: 6px 12px; /* Less padding */
}

.search-input {
  height: 26px !important; /* Even smaller height */
  font-size: 11px !important; /* Smaller font */
}

.search-icon {
  width: 12px;
  height: 12px;
}

/* More compact header */
.note-sidebar-header {
  height: 40px; /* Shorter header */
  padding: 8px 12px; /* Less padding */
}

.note-sidebar-header-text {
  font-size: 0.875rem; /* Slightly smaller font */
}

/* More compact footer */
.note-sidebar-footer {
  padding: 6px 12px; /* Less padding */
}

.new-note-button {
  height: 26px; /* Smaller height */
  font-size: 10px; /* Smaller font */
}

.new-note-icon {
  width: 11px;
  height: 11px;
}

/* Compact skeleton loading state */
.sidebar-loading .h-6 {
  height: 14px !important;
}

/* Improve separator spacing */
.my-2 {
  margin-top: 4px;
  margin-bottom: 4px;
}

/* Note: Old drop target styles removed to prevent conflicts with other components. */

/* Note: Old drop indicator and drag styles removed to prevent conflicts. */

/* Note: All old drag and drop styles removed to prevent conflicts with react-flow and other components.
   The new implementation uses headless-tree which provides its own drag and drop styling. */

/* Keep basic tree container styling but remove drag-specific styles */
.tree-container {
  padding: 4px 0;
  min-height: 50px;
  position: relative;
}

/* Editor content area */
.prose.prose-sm {
  max-width: none;
  width: 100%;
}

/* BlockNote editor content */
.bn-editor-content {
  width: 100% !important;
  max-width: 100% !important; /* Ensure full width */
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
}

/* BlockNote editor blocks */
.bn-block {
  width: 100% !important;
  background-color: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
}

/* Ensure editor is centered */
.blocknote-container .prose {
  width: 100%;
  max-width: 100%; /* Use full width */
  margin: 0 auto;
}