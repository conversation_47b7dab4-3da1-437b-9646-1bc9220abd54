"use client"

import { useRef, useEffect, useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, X, Calendar, Clock } from "lucide-react"
import * as echarts from 'echarts'
import {
  format, parse, startOfYear, endOfYear, eachMonthOfInterval,
  isSameMonth, isSameYear, subYears, addYears, startOfMonth,
  endOfMonth, eachDayOfInterval, getDate, getMonth, getYear
} from 'date-fns'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

interface ActivityData {
  date: string;
  count: number;
}

interface ActivityGraphProps {
  data?: {
    notes: ActivityData[];
    workflows: ActivityData[];
    chats: ActivityData[];
  };
  isLoading?: boolean;
}

export function ActivityGraph({ data, isLoading = false }: ActivityGraphProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [currentYear, setCurrentYear] = useState<Date>(new Date());
  const [chartInitialized, setChartInitialized] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null);
  const [viewMode, setViewMode] = useState<'year' | 'month' | 'week'>('year');

  // Navigation functions
  const handlePreviousYear = () => {
    setCurrentYear(prevDate => subYears(prevDate, 1));
  };

  const handleNextYear = () => {
    setCurrentYear(prevDate => addYears(prevDate, 1));
  };

  // Handle month selection
  const handleMonthClick = (monthIndex: number) => {
    if (!data) return;

    setSelectedMonth(monthIndex);
    setViewMode('month');

    // Reinitialize chart with the new view mode
    if (chartInitialized) {
      initializeChart();
    }
  };

  // Handle back to year view
  const handleBackToYearView = () => {
    setSelectedMonth(null);
    setViewMode('year');

    // Reinitialize chart with the year view
    if (chartInitialized) {
      initializeChart();
    }
  };

  // Generate daily data for the selected month
  const generateDailyData = useCallback(() => {
    if (!data || selectedMonth === null) return [];

    // Create a date for the first day of the selected month in the current year
    const selectedDate = new Date(getYear(currentYear), selectedMonth, 1);
    const monthStart = startOfMonth(selectedDate);
    const monthEnd = endOfMonth(selectedDate);

    // Get all days in the month
    const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });

    return daysInMonth.map(day => {
      // Filter activity data for this specific day
      const dayNotes = data.notes.filter(item => {
        const itemDate = new Date(item.date);
        return format(itemDate, 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd');
      });

      const dayWorkflows = data.workflows.filter(item => {
        const itemDate = new Date(item.date);
        return format(itemDate, 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd');
      });

      const dayChats = data.chats.filter(item => {
        const itemDate = new Date(item.date);
        return format(itemDate, 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd');
      });

      // Sum up counts for each activity type
      const notesCount = dayNotes.reduce((sum, item) => sum + item.count, 0);
      const workflowsCount = dayWorkflows.reduce((sum, item) => sum + item.count, 0);
      const chatsCount = dayChats.reduce((sum, item) => sum + item.count, 0);

      return {
        day: format(day, 'd'),
        fullDate: format(day, 'MMM dd'),
        notes: notesCount,
        workflows: workflowsCount,
        chats: chatsCount
      };
    });
  }, [data, selectedMonth, currentYear]);

  // Function to generate monthly data for the entire year
  const generateMonthlyData = useCallback(() => {
    if (!data) return [];

    const firstDayOfYear = startOfYear(currentYear);
    const lastDayOfYear = endOfYear(currentYear);
    const monthsInYear = eachMonthOfInterval({ start: firstDayOfYear, end: lastDayOfYear });

    return monthsInYear.map(month => {
      // Filter activity data for this month and year
      const monthNotes = data.notes.filter(item => {
        const itemDate = new Date(item.date);
        return isSameMonth(itemDate, month) && isSameYear(itemDate, month);
      });

      const monthWorkflows = data.workflows.filter(item => {
        const itemDate = new Date(item.date);
        return isSameMonth(itemDate, month) && isSameYear(itemDate, month);
      });

      const monthChats = data.chats.filter(item => {
        const itemDate = new Date(item.date);
        return isSameMonth(itemDate, month) && isSameYear(itemDate, month);
      });

      // Sum up counts for each activity type
      const notesCount = monthNotes.reduce((sum, item) => sum + item.count, 0);
      const workflowsCount = monthWorkflows.reduce((sum, item) => sum + item.count, 0);
      const chatsCount = monthChats.reduce((sum, item) => sum + item.count, 0);

      return {
        month: format(month, 'MMM'),
        notes: notesCount,
        workflows: workflowsCount,
        chats: chatsCount
      };
    });
  }, [data, currentYear]);

  // Initialize and update chart
  const initializeChart = useCallback(() => {
    if (!chartRef.current || !data) return;

    // Dispose existing chart if any
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // Create new chart instance
    chartInstance.current = echarts.init(chartRef.current);
    setChartInitialized(true);

    let chartData;
    let xAxisData;
    let tooltipText;
    let titleText;

    // Configure chart based on view mode
    if (viewMode === 'year') {
      // Year view - show all months
      chartData = generateMonthlyData();
      xAxisData = chartData.map(item => item.month);
      tooltipText = 'Click for daily view';
      titleText = `Activity Overview - ${format(currentYear, 'yyyy')}`;
    } else if (viewMode === 'month' && selectedMonth !== null) {
      // Month view - show days of the selected month
      chartData = generateDailyData();
      xAxisData = chartData.map(item => item.fullDate);

      // Get month name for the title
      const monthDate = new Date(getYear(currentYear), selectedMonth, 1);
      tooltipText = 'Daily activity details';
      titleText = `Activity for ${format(monthDate, 'MMMM yyyy')}`;
    } else {
      // Fallback to year view
      chartData = generateMonthlyData();
      xAxisData = chartData.map(item => item.month);
      tooltipText = 'Click for daily view';
      titleText = `Activity Overview - ${format(currentYear, 'yyyy')}`;
    }

    // Set chart options
    const option = {
      title: {
        text: titleText,
        left: 'center',
        top: 0,
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal',
          color: 'hsl(var(--foreground))'
        }
      },
      color: ['#2563eb', '#16a34a', '#dc2626'],
      grid: {
        top: 40,
        right: 20,
        bottom: 30,
        left: 40,
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: (params: any) => {
          let result = `${params[0].name}<br/>`;
          params.forEach((param: any) => {
            result += `<span style="display:inline-block;margin-right:4px;border-radius:50%;width:8px;height:8px;background-color:${param.color};"></span> ${param.seriesName}: ${param.value}<br/>`;
          });

          if (viewMode === 'year') {
            result += `<div style="margin-top:4px;font-style:italic;font-size:10px;">${tooltipText}</div>`;
          }

          return result;
        }
      },
      legend: {
        data: ['Notes', 'Workflows', 'Chats'],
        bottom: 0,
        textStyle: {
          color: 'hsl(var(--foreground))'
        }
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLabel: {
          fontSize: 12,
          color: 'hsl(var(--foreground))',
          // Make labels look clickable in year view
          rich: {
            a: {
              color: 'hsl(var(--foreground))',
              fontWeight: viewMode === 'year' ? 'bold' : 'normal',
              lineHeight: 20,
              align: 'center'
            }
          },
          formatter: function (value: string) {
            return '{a|' + value + '}';
          },
          rotate: viewMode === 'month' ? 45 : 0
        },
        splitLine: { show: false }
      },
      yAxis: {
        type: 'value',
        axisLabel: { fontSize: 12 },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            opacity: 0.3
          }
        }
      },
      series: [
        {
          name: 'Notes',
          type: 'line',
          data: chartData.map(item => item.notes),
          showSymbol: true,
          symbolSize: 6,
          smooth: true,
          lineStyle: { width: 2 },
          areaStyle: { opacity: 0.1 }
        },
        {
          name: 'Workflows',
          type: 'line',
          data: chartData.map(item => item.workflows),
          showSymbol: true,
          symbolSize: 6,
          smooth: true,
          lineStyle: { width: 2 },
          areaStyle: { opacity: 0.1 }
        },
        {
          name: 'Chats',
          type: 'line',
          data: chartData.map(item => item.chats),
          showSymbol: true,
          symbolSize: 6,
          smooth: true,
          lineStyle: { width: 2 },
          areaStyle: { opacity: 0.1 }
        }
      ]
    };

    // Add click event handler only in year view
    if (viewMode === 'year') {
      chartInstance.current.on('click', (params: any) => {
        if (params.componentType === 'xAxis') {
          // Get month index from month name
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          const monthIndex = monthNames.indexOf(params.value);
          if (monthIndex !== -1) {
            handleMonthClick(monthIndex);
          }
        } else if (params.componentType === 'series') {
          // Get month index from x-axis index
          const monthIndex = params.dataIndex;
          handleMonthClick(monthIndex);
        }
      });
    }

    // Apply options
    chartInstance.current.setOption(option);
  }, [data, generateMonthlyData, generateDailyData, viewMode, selectedMonth, currentYear]);

  // Initialize chart when component mounts or data changes
  useEffect(() => {
    if (!isLoading && data) {
      // Use a small timeout to ensure the DOM is ready
      const timer = setTimeout(() => {
        initializeChart();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [data, isLoading, initializeChart]);

  // Update chart when year changes
  useEffect(() => {
    if (chartInitialized && data) {
      initializeChart();
    }
  }, [currentYear, chartInitialized, initializeChart, data]);

  // Add resize handler
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Activity Overview</CardTitle>
          <CardDescription>Loading activity data...</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center">
          <div className="text-muted-foreground">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  if (!data || (!data.notes.length && !data.workflows.length && !data.chats.length)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Activity Overview</CardTitle>
          <CardDescription>No activity yet</CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center">
          <div className="text-muted-foreground">Start creating notes, workflows, or chats to see activity</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Activity Overview</CardTitle>
            <CardDescription>
              {viewMode === 'year'
                ? `${format(currentYear, 'yyyy')} - Total notes, workflows, and chats`
                : selectedMonth !== null
                  ? `${format(new Date(getYear(currentYear), selectedMonth, 1), 'MMMM yyyy')} - Daily activity`
                  : 'Activity Overview'
              }
            </CardDescription>
          </div>
          <div className="flex items-center gap-1">
            {viewMode === 'month' ? (
              <Button
                onClick={handleBackToYearView}
                variant="outline"
                size="sm"
                className="h-8"
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Back to Year View
              </Button>
            ) : (
              <>
                <Button onClick={handlePreviousYear} variant="outline" size="icon" className="h-8 w-8">
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button onClick={handleNextYear} variant="outline" size="icon" className="h-8 w-8">
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="h-[300px]">
        <div ref={chartRef} className="w-full h-full" />
        <div className="text-xs text-center mt-2 text-muted-foreground">
          {viewMode === 'year'
            ? 'Click on any month to see detailed daily activity'
            : 'Showing daily activity data for the selected month'
          }
        </div>
      </CardContent>
    </Card>
  )
}
