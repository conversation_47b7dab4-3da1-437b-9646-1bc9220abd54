import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Dataset } from '@/types/index';
import { UseDatasetHandlingReturn } from './types';
import { checkInternetConnection } from './chartBuilderUtils';
import { mockDatasets, getAllMockDatasets } from '@/utils/mockData';

/**
 * Hook for handling datasets in the ChartBuilder
 * @returns Dataset handling functions and state
 */
export const useDatasetHandling = (): UseDatasetHandlingReturn => {
  // Store all loaded datasets in a cache for quick access
  const [datasetCache, setDatasetCache] = useState<Record<string, Dataset>>({});
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [isLoadingDatasets, setIsLoadingDatasets] = useState(true);

  // Function to fetch datasets (can be called manually)
  const fetchDatasets = async () => {
      try {
        setIsLoadingDatasets(true);
        console.log('useDatasetHandling: Starting to fetch datasets...');

        // Check for internet connection first
        const online = await checkInternetConnection();
        console.log('useDatasetHandling: Internet connection status:', online);

        if (online) {
          console.log('useDatasetHandling: Fetching datasets and folders from API...');

          // Fetch datasets and folders from API
          const [datasetsResponse, foldersResponse] = await Promise.all([
            fetch('/api/datasets'),
            fetch('/api/datasets/folders')
          ]);

          console.log('useDatasetHandling: API responses received:', {
            datasetsStatus: datasetsResponse.status,
            foldersStatus: foldersResponse.status
          });

          // Check authentication for both requests
          if (datasetsResponse.status === 401 || foldersResponse.status === 401) {
            console.warn('useDatasetHandling: Authentication required - user not signed in, using mock data');
            throw new Error('Authentication required - using mock data');
          }

          if (!datasetsResponse.ok || !foldersResponse.ok) {
            console.error(`useDatasetHandling: API Error - Datasets ${datasetsResponse.status}, Folders ${foldersResponse.status}`);

            // Try to get error details
            try {
              const datasetsError = !datasetsResponse.ok ? await datasetsResponse.text() : null;
              const foldersError = !foldersResponse.ok ? await foldersResponse.text() : null;
              console.error('useDatasetHandling: Error details:', { datasetsError, foldersError });
            } catch (e) {
              console.error('useDatasetHandling: Could not parse error details');
            }

            throw new Error(`Failed to fetch data: ${datasetsResponse.status}/${foldersResponse.status}`);
          }

          const [datasetsData, foldersData] = await Promise.all([
            datasetsResponse.json(),
            foldersResponse.json()
          ]);

          console.log('useDatasetHandling: API Responses received:', {
            datasetsSuccess: datasetsData.success,
            datasetsCount: datasetsData.datasets?.length || 0,
            foldersSuccess: foldersData.success,
            foldersCount: foldersData.folders?.length || 0,
            rootDatasetsFromFolders: foldersData.datasets?.length || 0
          });

          // Collect all datasets from both root and folders
          const allDatasets: any[] = [];

          // Add root datasets (not in folders)
          if (datasetsData.success && Array.isArray(datasetsData.datasets)) {
            console.log(`useDatasetHandling: Adding ${datasetsData.datasets.length} root datasets from /api/datasets`);
            allDatasets.push(...datasetsData.datasets);
          }

          // Add datasets from folders
          if (foldersData.success) {
            // Add root datasets from folders response
            if (Array.isArray(foldersData.datasets)) {
              console.log(`useDatasetHandling: Adding ${foldersData.datasets.length} root datasets from /api/datasets/folders`);
              allDatasets.push(...foldersData.datasets);
            }

            // Add datasets from each folder
            if (Array.isArray(foldersData.folders)) {
              let folderDatasetCount = 0;
              foldersData.folders.forEach((folder: any) => {
                if (Array.isArray(folder.datasets)) {
                  folderDatasetCount += folder.datasets.length;
                  allDatasets.push(...folder.datasets);
                }
              });
              console.log(`useDatasetHandling: Adding ${folderDatasetCount} datasets from ${foldersData.folders.length} folders`);
            }
          }

          console.log(`useDatasetHandling: Total datasets collected before deduplication: ${allDatasets.length}`);

          // Remove duplicates based on ID
          const uniqueDatasets = allDatasets.filter((dataset, index, self) =>
            index === self.findIndex(d => d.id === dataset.id)
          );

          console.log(`useDatasetHandling: Unique datasets after deduplication: ${uniqueDatasets.length}`);

          if (uniqueDatasets.length > 0) {
            // Transform API data to match our Dataset interface
            const transformedDatasets = uniqueDatasets.map((ds: any) => ({
              id: ds.id,
              name: ds.name,
              data: ds.data || [],
              columns: ds.headers?.map((header: string) => ({ name: header, type: 'string' })) || [],
              headers: ds.headers || [],
              fileType: ds.fileType || 'json',
              createdAt: new Date(ds.createdAt),
              description: ds.description
            }));

            setDatasets(transformedDatasets);
            console.log(`useDatasetHandling: Successfully loaded ${transformedDatasets.length} datasets from database`);
            if (transformedDatasets.length > 0) {
              console.log('useDatasetHandling: Dataset names:', transformedDatasets.map(ds => ds.name));
            }

            // Also populate the cache with real data
            const realCache: Record<string, Dataset> = {};
            transformedDatasets.forEach(ds => {
              realCache[ds.id] = ds;
            });
            setDatasetCache(realCache);

            if (transformedDatasets.length > 0) {
              toast.success(`Loaded ${transformedDatasets.length} datasets from database`);
            } else {
              toast.info('No datasets found in your database. Upload some datasets to get started.');
            }
            return; // Exit early on success - even if no datasets found, don't fall back to mock data when online
          } else {
            // When online but no datasets found, don't fall back to mock data
            console.log('useDatasetHandling: No datasets found in API response - showing empty state');
            setDatasets([]);
            setDatasetCache({});
            toast.info('No datasets found in your database. Upload some datasets to get started.');
            return; // Exit early - don't fall back to mock data when online
          }
        } else {
          // Use mock datasets in offline mode
          throw new Error('No internet connection, using mock data');
        }
      } catch (error) {
        console.warn('useDatasetHandling: Error fetching datasets:', error);

        // Check if we're offline - only use mock data if offline
        const isOffline = await checkInternetConnection().then(online => !online).catch(() => true);

        if (isOffline) {
          console.log('useDatasetHandling: Offline mode detected, using mock datasets');

          // Load mock datasets only when offline
          const mockData = getAllMockDatasets();
          setDatasets(mockData.map(ds => ({
            id: ds.id,
            name: ds.name,
            data: ds.data,
            columns: Object.keys(ds.data[0] || {}).map(key => ({ name: key, type: 'string' })),
            headers: Object.keys(ds.data[0] || {}),
            fileType: 'json',
            createdAt: new Date(ds.createdAt)
          })));

          // Also initialize the dataset cache with mock data to skip network requests later
          const mockCache: Record<string, Dataset> = {};
          mockData.forEach(ds => {
            mockCache[ds.id] = {
              id: ds.id,
              name: ds.name,
              data: ds.data,
              columns: Object.keys(ds.data[0] || {}).map(key => ({ name: key, type: 'string' })),
              headers: Object.keys(ds.data[0] || {}),
              fileType: 'json',
              createdAt: new Date(ds.createdAt)
            };
          });
          setDatasetCache(mockCache);

          toast.info(`Using offline mock datasets (${mockData.length} available)`);
        } else {
          // When online but API failed, show empty state instead of mock data
          console.log('useDatasetHandling: Online but API failed, showing empty state');
          setDatasets([]);
          setDatasetCache({});
          toast.error('Failed to load datasets from database. Please try refreshing the page.');
        }
      } finally {
        setIsLoadingDatasets(false);
      }
    };

  // Fetch datasets on component mount
  useEffect(() => {
    fetchDatasets();
  }, []);

  // Handle dataset selection for a specific cell
  const handleSelectDatasets = async (cellId: string, datasetIds: string[]) => {
    try {
      const selectedList: Dataset[] = [];
      const newCache = { ...datasetCache };

      // Fetch each selected dataset
      for (const datasetId of datasetIds) {
        // Check if we already have this dataset in the cache
        if (newCache[datasetId]) {
          selectedList.push(newCache[datasetId]);
          continue;
        }

        // Check internet connection
        const isOnline = await checkInternetConnection();

        if (isOnline) {
          // Fetch from API
          const response = await fetch(`/api/datasets?datasetId=${datasetId}`);

          if (response.status === 401) {
            console.warn(`Authentication required for dataset ${datasetId}`);
            toast.error(`Authentication required for dataset ${datasetId}`);
            continue; // Skip this dataset when online but auth failed
          } else if (!response.ok) {
            console.warn(`Failed to load dataset ${datasetId} from API (${response.status})`);
            toast.error(`Failed to load dataset ${datasetId} from database`);
            continue; // Skip this dataset when online but API failed
          } else {
            const data = await response.json();
            if (data.success && data.datasetInfo) {
              const dataset: Dataset = {
                id: datasetId,
                name: data.datasetInfo.name,
                data: data.datasetInfo.data,
                columns: data.datasetInfo.headers?.map((header: string) => ({ name: header, type: 'string' })) || [],
                headers: data.datasetInfo.headers,
                fileType: data.datasetInfo.fileType,
                createdAt: new Date(data.datasetInfo.createdAt)
              };
              selectedList.push(dataset);
              newCache[datasetId] = dataset;
              continue; // Skip to next dataset
            } else {
              console.warn(`Invalid dataset response for ${datasetId}`);
              toast.error(`Invalid response for dataset ${datasetId}`);
              continue; // Skip this dataset when online but invalid response
            }
          }
        } else {
          // Only use mock data when offline
          const mockDs = mockDatasets.find(ds => ds.id === datasetId);
          if (mockDs) {
            const dataset: Dataset = {
              id: datasetId,
              name: mockDs.name,
              data: mockDs.data,
              columns: Object.keys(mockDs.data[0] || {}).map(key => ({ name: key, type: 'string' })),
              headers: Object.keys(mockDs.data[0] || {}),
              fileType: 'json',
              createdAt: new Date(mockDs.createdAt)
            };
            selectedList.push(dataset);
            newCache[datasetId] = dataset;
          } else {
            toast.error(`Dataset ${datasetId} not found (offline mode)`);
          }
        }
      }

      // Update the dataset cache with any new datasets
      setDatasetCache(newCache);

      if (selectedList.length > 0) {
        toast.success(`${selectedList.length} dataset(s) selected for cell`);
      }

      return { selectedList, datasetIds };
    } catch (error) {
      console.error('Failed to load datasets:', error);
      toast.error('Failed to load one or more datasets');
      throw error;
    }
  };

  return {
    datasets,
    datasetCache,
    isLoadingDatasets,
    handleSelectDatasets,
    refreshDatasets: fetchDatasets
  };
};
